{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Delete Supplier" %} - {{ object.name }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>{% trans "Delete Supplier" %}
            </h1>
            <p class="text-muted">{% trans "Confirm supplier deletion" %}</p>
        </div>
        <div>
            <a href="{% url 'products:supplier_detail' object.pk %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Supplier" %}
            </a>
        </div>
    </div>
    
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Warning Card -->
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>{% trans "Warning: This action cannot be undone" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger" role="alert">
                        <h6 class="alert-heading">{% trans "Are you sure you want to delete this supplier?" %}</h6>
                        <p class="mb-0">
                            {% trans "You are about to permanently delete" %} <strong>{{ object.name }}</strong>. 
                            {% trans "This action cannot be undone and will affect the following:" %}
                        </p>
                    </div>
                    
                    <!-- Supplier Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>{% trans "Supplier Details" %}</h6>
                            <ul class="list-unstyled">
                                <li><strong>{% trans "Name" %}:</strong> {{ object.name }}</li>
                                {% if object.contact_person %}
                                    <li><strong>{% trans "Contact Person" %}:</strong> {{ object.contact_person }}</li>
                                {% endif %}
                                <li><strong>{% trans "Phone" %}:</strong> {{ object.phone }}</li>
                                {% if object.email %}
                                    <li><strong>{% trans "Email" %}:</strong> {{ object.email }}</li>
                                {% endif %}
                                <li><strong>{% trans "Location" %}:</strong> 
                                    {% if object.city %}{{ object.city }}, {% endif %}{{ object.country }}
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>{% trans "Impact Assessment" %}</h6>
                            <ul class="list-unstyled">
                                <li>
                                    <i class="fas fa-box text-warning me-2"></i>
                                    <strong>{{ object.products.count }}</strong> {% trans "products will lose their supplier reference" %}
                                </li>
                                {% if object.products.count > 0 %}
                                    <li>
                                        <i class="fas fa-exclamation-circle text-danger me-2"></i>
                                        {% trans "Product cost tracking may be affected" %}
                                    </li>
                                    <li>
                                        <i class="fas fa-chart-line text-info me-2"></i>
                                        {% trans "Historical supplier reports will show 'Deleted Supplier'" %}
                                    </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                    
                    <!-- Products List (if any) -->
                    {% if object.products.exists %}
                        <div class="mb-4">
                            <h6>{% trans "Affected Products" %}</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>{% trans "Product Name" %}</th>
                                            <th>{% trans "SKU" %}</th>
                                            <th>{% trans "Category" %}</th>
                                            <th class="text-end">{% trans "Stock" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for product in object.products.all|slice:":10" %}
                                            <tr>
                                                <td>{{ product.name }}</td>
                                                <td><code>{{ product.sku }}</code></td>
                                                <td>
                                                    {% if product.category %}
                                                        {{ product.category.name }}
                                                    {% else %}
                                                        <span class="text-muted">{% trans "No Category" %}</span>
                                                    {% endif %}
                                                </td>
                                                <td class="text-end">{{ product.stock_quantity }}</td>
                                            </tr>
                                        {% endfor %}
                                        {% if object.products.count > 10 %}
                                            <tr>
                                                <td colspan="4" class="text-center text-muted">
                                                    {% trans "... and" %} {{ object.products.count|add:"-10" }} {% trans "more products" %}
                                                </td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    {% endif %}
                    
                    <!-- Alternatives -->
                    <div class="alert alert-info" role="alert">
                        <h6 class="alert-heading">{% trans "Consider these alternatives:" %}</h6>
                        <ul class="mb-0">
                            <li>{% trans "Mark the supplier as inactive instead of deleting" %}</li>
                            <li>{% trans "Transfer products to another supplier before deletion" %}</li>
                            <li>{% trans "Export supplier data for your records" %}</li>
                        </ul>
                    </div>
                    
                    <!-- Confirmation Form -->
                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                            <label class="form-check-label" for="confirmDelete">
                                {% trans "I understand that this action cannot be undone and I want to permanently delete this supplier." %}
                            </label>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <div>
                                <a href="{% url 'products:supplier_update' object.pk %}" class="btn btn-outline-warning">
                                    <i class="fas fa-edit me-2"></i>{% trans "Mark as Inactive Instead" %}
                                </a>
                            </div>
                            <div>
                                <a href="{% url 'products:supplier_detail' object.pk %}" class="btn btn-outline-secondary me-2">
                                    <i class="fas fa-times me-2"></i>{% trans "Cancel" %}
                                </a>
                                <button type="submit" class="btn btn-danger" id="deleteButton" disabled>
                                    <i class="fas fa-trash me-2"></i>{% trans "Delete Supplier" %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkbox = document.getElementById('confirmDelete');
    const deleteButton = document.getElementById('deleteButton');
    
    checkbox.addEventListener('change', function() {
        deleteButton.disabled = !this.checked;
    });
});
</script>
{% endblock %}
