{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Categories" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{% trans "Product Categories" %}</h1>
            <p class="text-muted">{% trans "Organize your products into categories" %}</p>
        </div>
        <div class="btn-group" role="group">
            <a href="{% url 'products:category_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>{% trans "Add Category" %}
            </a>
            <a href="{% url 'products:product_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Products" %}
            </a>
        </div>
    </div>
    
    <!-- Categories Grid -->
    {% if categories %}
        <div class="row">
            {% for category in categories %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <h5 class="card-title mb-0">{{ category.name }}</h5>
                                <span class="badge bg-{% if category.is_active %}success{% else %}danger{% endif %}">
                                    {% if category.is_active %}{% trans "Active" %}{% else %}{% trans "Inactive" %}{% endif %}
                                </span>
                            </div>
                            
                            {% if category.description %}
                                <p class="card-text text-muted">{{ category.description|truncatechars:100 }}</p>
                            {% endif %}
                            
                            <div class="row text-center mt-3">
                                <div class="col-6">
                                    <h6 class="text-primary">{{ category.product_set.count }}</h6>
                                    <small class="text-muted">{% trans "Products" %}</small>
                                </div>
                                <div class="col-6">
                                    <h6 class="text-muted">{{ category.created_at|date:"M Y" }}</h6>
                                    <small class="text-muted">{% trans "Created" %}</small>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="btn-group w-100" role="group">
                                <a href="{% url 'products:category_update' category.pk %}" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-edit me-1"></i>{% trans "Edit" %}
                                </a>
                                <a href="{% url 'products:product_list' %}?category={{ category.pk }}" 
                                   class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-eye me-1"></i>{% trans "View Products" %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
            <nav aria-label="{% trans 'Categories pagination' %}">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1">{% trans "First" %}</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                        </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">
                            {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                        </span>
                    </li>
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">{% trans "Last" %}</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-tags text-muted" style="font-size: 3rem;"></i>
            <h5 class="mt-3 text-muted">{% trans "No categories found" %}</h5>
            <p class="text-muted">{% trans "Create your first category to organize your products." %}</p>
            <a href="{% url 'products:category_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>{% trans "Create First Category" %}
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}
