from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import CreateView, UpdateView, ListView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q, Sum
from django.utils import timezone
import csv
from .models import Customer
from .forms import CustomerForm, CustomerSearchForm, CustomerPaymentForm
from accounts.models import User


def can_manage_customers(user):
    """Check if user can manage customers"""
    return user.is_authenticated and user.role in [User.UserRole.ADMIN, User.UserRole.SALES_EMPLOYEE]


class CustomerManagementMixin(UserPassesTestMixin):
    """Mixin to require customer management permissions"""
    def test_func(self):
        return can_manage_customers(self.request.user)


@login_required
@user_passes_test(can_manage_customers)
def customer_list(request):
    """List all customers with search and filtering"""
    form = CustomerSearchForm(request.GET)
    customers = Customer.objects.order_by('-created_at')

    if form.is_valid():
        search = form.cleaned_data.get('search')
        customer_type = form.cleaned_data.get('customer_type')
        is_active = form.cleaned_data.get('is_active')
        has_outstanding_balance = form.cleaned_data.get('has_outstanding_balance')

        if search:
            customers = customers.filter(
                Q(name__icontains=search) |
                Q(company_name__icontains=search) |
                Q(phone__icontains=search) |
                Q(email__icontains=search)
            )

        if customer_type:
            customers = customers.filter(customer_type=customer_type)

        if is_active:
            customers = customers.filter(is_active=is_active == 'true')

        if has_outstanding_balance:
            if has_outstanding_balance == 'true':
                # Filter customers with outstanding invoices
                customers = customers.filter(invoices__status__in=['pending', 'overdue']).distinct()
            elif has_outstanding_balance == 'false':
                # Filter customers without outstanding invoices
                customers = customers.exclude(invoices__status__in=['pending', 'overdue']).distinct()

    # Pagination
    paginator = Paginator(customers, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'customers': page_obj,
        'form': form,
        'is_paginated': page_obj.has_other_pages(),
        'page_obj': page_obj,
    }

    return render(request, 'customers/customer_list.html', context)


class CustomerCreateView(CustomerManagementMixin, CreateView):
    """Create new customer"""
    model = Customer
    form_class = CustomerForm
    template_name = 'customers/customer_create.html'
    success_url = reverse_lazy('customers:customer_list')

    def form_valid(self, form):
        messages.success(self.request, _('Customer created successfully.'))
        return super().form_valid(form)


class CustomerUpdateView(CustomerManagementMixin, UpdateView):
    """Update existing customer"""
    model = Customer
    form_class = CustomerForm
    template_name = 'customers/customer_update.html'
    success_url = reverse_lazy('customers:customer_list')

    def form_valid(self, form):
        messages.success(self.request, _('Customer updated successfully.'))
        return super().form_valid(form)


class CustomerDeleteView(CustomerManagementMixin, DeleteView):
    """Delete customer"""
    model = Customer
    template_name = 'customers/customer_confirm_delete.html'
    success_url = reverse_lazy('customers:customer_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Customer deleted successfully.'))
        return super().delete(request, *args, **kwargs)


@login_required
@user_passes_test(can_manage_customers)
def customer_detail(request, pk):
    """Customer detail view with purchase history"""
    customer = get_object_or_404(Customer, pk=pk)

    # Get customer's invoices
    invoices = customer.invoices.order_by('-created_at')[:10]

    # Calculate statistics
    stats = {
        'total_invoices': customer.invoices.count(),
        'total_purchases': customer.total_purchases,
        'outstanding_balance': customer.outstanding_balance,
        'available_credit': customer.available_credit,
    }

    context = {
        'customer': customer,
        'invoices': invoices,
        'stats': stats,
    }

    return render(request, 'customers/customer_detail.html', context)


@login_required
@user_passes_test(can_manage_customers)
def customer_purchase_history(request, pk):
    """Customer purchase history view"""
    customer = get_object_or_404(Customer, pk=pk)
    invoices = customer.invoices.order_by('-created_at')

    # Pagination
    paginator = Paginator(invoices, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'customer': customer,
        'invoices': page_obj,
        'is_paginated': page_obj.has_other_pages(),
        'page_obj': page_obj,
    }

    return render(request, 'customers/customer_purchase_history.html', context)


@login_required
@user_passes_test(can_manage_customers)
def export_customers(request):
    """Export customers to CSV"""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="customers.csv"'

    writer = csv.writer(response)
    writer.writerow([
        'Name', 'Type', 'Company', 'Phone', 'Email', 'Address',
        'Tax ID', 'Credit Limit', 'Payment Terms', 'Active'
    ])

    customers = Customer.objects.all()
    for customer in customers:
        writer.writerow([
            customer.name,
            customer.get_customer_type_display(),
            customer.company_name or '',
            customer.phone,
            customer.email or '',
            customer.address or '',
            customer.tax_id or '',
            customer.credit_limit,
            customer.payment_terms,
            'Yes' if customer.is_active else 'No'
        ])

    return response


@login_required
def customers_with_outstanding_balance(request):
    """View customers with outstanding balances (accessible to accountants)"""
    if not (request.user.role in [User.UserRole.ADMIN, User.UserRole.ACCOUNTANT]):
        messages.error(request, _('You do not have permission to view this page.'))
        return redirect('dashboard:home')

    # Get customers with outstanding invoices
    customers = Customer.objects.filter(
        invoices__status__in=['pending', 'overdue'],
        is_active=True
    ).distinct().order_by('name')

    # Add outstanding balance to each customer
    customer_data = []
    for customer in customers:
        outstanding = customer.outstanding_balance
        if outstanding > 0:
            customer_data.append({
                'customer': customer,
                'outstanding_balance': outstanding,
                'overdue_days': 0,  # This would need calculation based on due dates
            })

    context = {
        'customer_data': customer_data,
        'total_outstanding': sum(item['outstanding_balance'] for item in customer_data),
    }

    return render(request, 'customers/outstanding_balances.html', context)
