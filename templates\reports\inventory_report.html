{% extends 'base.html' %}
{% load i18n crispy_forms_tags %}

{% block title %}{% trans "Inventory Report" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{% trans "Inventory Report" %}</h1>
            <p class="text-muted">{% trans "Monitor stock levels and inventory valuation" %}</p>
        </div>
        <div class="btn-group" role="group">
            {% if report_data %}
                <a href="{% url 'reports:export_report' 'inventory' %}?{{ request.GET.urlencode }}&format=csv" 
                   class="btn btn-outline-primary">
                    <i class="fas fa-download me-2"></i>{% trans "Export CSV" %}
                </a>
            {% endif %}
            <a href="{% url 'reports:reports_home' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Reports" %}
            </a>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-filter me-2"></i>{% trans "Report Filters" %}
            </h6>
        </div>
        <div class="card-body">
            <form method="get">
                {% crispy form %}
            </form>
        </div>
    </div>
    
    {% if report_data %}
        <!-- Summary Cards -->
        {% if report_data.report_type == 'current_stock' %}
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">{{ report_data.total_products }}</h4>
                                <p class="mb-0">{% trans "Total Products" %}</p>
                            </div>
                            <div>
                                <i class="fas fa-box fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">${{ report_data.total_stock_value|floatformat:2 }}</h4>
                                <p class="mb-0">{% trans "Stock Value (Cost)" %}</p>
                            </div>
                            <div>
                                <i class="fas fa-dollar-sign fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">${{ report_data.total_retail_value|floatformat:2 }}</h4>
                                <p class="mb-0">{% trans "Retail Value" %}</p>
                            </div>
                            <div>
                                <i class="fas fa-tags fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">${{ report_data.total_retail_value|add:report_data.total_stock_value|floatformat:2 }}</h4>
                                <p class="mb-0">{% trans "Profit Potential" %}</p>
                            </div>
                            <div>
                                <i class="fas fa-chart-line fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
        
        {% if report_data.report_type == 'valuation' %}
        <div class="row mb-4">
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-0">${{ report_data.total_cost_value|floatformat:2 }}</h4>
                        <p class="mb-0">{% trans "Total Cost Value" %}</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-0">${{ report_data.total_retail_value|floatformat:2 }}</h4>
                        <p class="mb-0">{% trans "Total Retail Value" %}</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-0">${{ report_data.total_profit_potential|floatformat:2 }}</h4>
                        <p class="mb-0">{% trans "Profit Potential" %}</p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
        
        <div class="row">
            <!-- Main Report Content -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            {% if report_data.report_type == 'current_stock' %}
                                {% trans "Current Stock Levels" %}
                            {% elif report_data.report_type == 'low_stock' %}
                                {% trans "Low Stock Items" %}
                            {% elif report_data.report_type == 'out_of_stock' %}
                                {% trans "Out of Stock Items" %}
                            {% elif report_data.report_type == 'valuation' %}
                                {% trans "Inventory Valuation" %}
                            {% endif %}
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        {% if report_data.products or report_data.valuation_data %}
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Product" %}</th>
                                            <th>{% trans "SKU" %}</th>
                                            <th>{% trans "Category" %}</th>
                                            {% if report_data.report_type == 'valuation' %}
                                                <th class="text-end">{% trans "Stock Qty" %}</th>
                                                <th class="text-end">{% trans "Cost Value" %}</th>
                                                <th class="text-end">{% trans "Retail Value" %}</th>
                                                <th class="text-end">{% trans "Profit Potential" %}</th>
                                            {% else %}
                                                <th class="text-end">{% trans "Current Stock" %}</th>
                                                <th class="text-end">{% trans "Min Level" %}</th>
                                                <th class="text-end">{% trans "Unit Price" %}</th>
                                                <th>{% trans "Status" %}</th>
                                            {% endif %}
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% if report_data.valuation_data %}
                                            {% for item in report_data.valuation_data %}
                                                <tr>
                                                    <td>
                                                        <div class="fw-bold">{{ item.product.name }}</div>
                                                        {% if item.product.description %}
                                                            <small class="text-muted">{{ item.product.description|truncatechars:50 }}</small>
                                                        {% endif %}
                                                    </td>
                                                    <td><code>{{ item.product.sku }}</code></td>
                                                    <td>
                                                        {% if item.product.category %}
                                                            <span class="badge bg-info">{{ item.product.category.name }}</span>
                                                        {% else %}
                                                            <span class="text-muted">{% trans "No Category" %}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td class="text-end">{{ item.product.stock_quantity }}</td>
                                                    <td class="text-end">${{ item.cost_value|floatformat:2 }}</td>
                                                    <td class="text-end">${{ item.retail_value|floatformat:2 }}</td>
                                                    <td class="text-end text-success">${{ item.profit_potential|floatformat:2 }}</td>
                                                </tr>
                                            {% endfor %}
                                        {% else %}
                                            {% for product in report_data.products %}
                                                <tr>
                                                    <td>
                                                        <div class="fw-bold">{{ product.name }}</div>
                                                        {% if product.description %}
                                                            <small class="text-muted">{{ product.description|truncatechars:50 }}</small>
                                                        {% endif %}
                                                    </td>
                                                    <td><code>{{ product.sku }}</code></td>
                                                    <td>
                                                        {% if product.category %}
                                                            <span class="badge bg-info">{{ product.category.name }}</span>
                                                        {% else %}
                                                            <span class="text-muted">{% trans "No Category" %}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td class="text-end">
                                                        <span class="fw-bold {% if product.stock_quantity <= product.min_stock_level %}text-danger{% elif product.stock_quantity <= product.min_stock_level|add:10 %}text-warning{% else %}text-success{% endif %}">
                                                            {{ product.stock_quantity }}
                                                        </span>
                                                    </td>
                                                    <td class="text-end">{{ product.min_stock_level }}</td>
                                                    <td class="text-end">${{ product.selling_price }}</td>
                                                    <td>
                                                        {% if product.stock_quantity == 0 %}
                                                            <span class="badge bg-danger">{% trans "Out of Stock" %}</span>
                                                        {% elif product.stock_quantity <= product.min_stock_level %}
                                                            <span class="badge bg-warning">{% trans "Low Stock" %}</span>
                                                        {% else %}
                                                            <span class="badge bg-success">{% trans "In Stock" %}</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-box text-muted" style="font-size: 3rem;"></i>
                                <h5 class="mt-3 text-muted">{% trans "No products found" %}</h5>
                                <p class="text-muted">{% trans "No products match the selected criteria." %}</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Category Breakdown -->
            <div class="col-lg-4">
                {% if report_data.category_breakdown %}
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-pie me-2"></i>{% trans "Category Breakdown" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        {% for category in report_data.category_breakdown %}
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <div class="fw-bold">{{ category.category__name|default:"No Category" }}</div>
                                    <small class="text-muted">{{ category.product_count }} {% trans "products" %}</small>
                                </div>
                                <div class="text-end">
                                    <div class="fw-bold text-primary">${{ category.total_value|floatformat:2 }}</div>
                                    <small class="text-muted">{{ category.total_stock }} {% trans "units" %}</small>
                                </div>
                            </div>
                            {% if not forloop.last %}<hr class="my-2">{% endif %}
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                
                <!-- Quick Actions -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>{% trans "Quick Actions" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{% url 'products:product_create' %}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-plus me-2"></i>{% trans "Add Product" %}
                            </a>
                            <a href="{% url 'products:product_list' %}" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-list me-2"></i>{% trans "Manage Products" %}
                            </a>
                            <a href="{% url 'products:bulk_import' %}" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-upload me-2"></i>{% trans "Import Products" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    {% else %}
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-warehouse text-muted" style="font-size: 3rem;"></i>
                <h5 class="mt-3 text-muted">{% trans "Generate Inventory Report" %}</h5>
                <p class="text-muted">{% trans "Select your report type and filters above, then click 'Generate Report'." %}</p>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
