{% extends 'base.html' %}
{% load i18n crispy_forms_tags %}

{% block title %}{% trans "Edit" %} {{ object.name }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">{% trans "Edit Product" %}</h1>
                    <p class="text-muted">{% trans "Update product information" %}</p>
                </div>
                <div class="btn-group" role="group">
                    <a href="{% url 'products:product_detail' object.pk %}" class="btn btn-outline-info">
                        <i class="fas fa-eye me-2"></i>{% trans "View Details" %}
                    </a>
                    <a href="{% url 'products:product_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Products" %}
                    </a>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-edit me-2"></i>{% trans "Product Information" %}
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        {% crispy form %}
                    </form>
                </div>
            </div>
            
            <!-- Current Product Info -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>{% trans "Current Information" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-muted">{% trans "Current Stock" %}:</span>
                                <span class="fw-bold">{{ object.stock_quantity }}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-muted">{% trans "Profit Margin" %}:</span>
                                <span class="badge bg-{% if object.profit_margin > 20 %}success{% elif object.profit_margin > 10 %}warning{% else %}danger{% endif %}">
                                    {{ object.profit_margin|floatformat:2 }}%
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-muted">{% trans "Created" %}:</span>
                                <span>{{ object.created_at|date:"M d, Y" }}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-muted">{% trans "Last Updated" %}:</span>
                                <span>{{ object.updated_at|date:"M d, Y" }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Calculate profit margin
    function calculateMargin() {
        const costPrice = parseFloat($('#id_cost_price').val()) || 0;
        const sellingPrice = parseFloat($('#id_selling_price').val()) || 0;
        
        if (costPrice > 0) {
            const margin = ((sellingPrice - costPrice) / costPrice * 100).toFixed(2);
            $('#profit-margin').text(margin + '%');
        }
    }
    
    $('#id_cost_price, #id_selling_price').on('input', calculateMargin);
    
    // Show current image preview
    $('#id_product_image').on('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#image-preview').html('<img src="' + e.target.result + '" class="img-fluid rounded mt-2" style="max-height: 200px;">');
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>
{% endblock %}
