from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import Customer


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    """Customer Admin"""
    list_display = ['name', 'customer_type', 'phone', 'email', 'credit_limit', 'is_active', 'created_at']
    list_filter = ['customer_type', 'is_active', 'created_at']
    search_fields = ['name', 'company_name', 'phone', 'email']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('customer_type', 'name', 'company_name')
        }),
        (_('Contact Information'), {
            'fields': ('phone', 'email', 'address')
        }),
        (_('Business Information'), {
            'fields': ('tax_id', 'credit_limit', 'payment_terms')
        }),
        (_('Additional Information'), {
            'fields': ('notes', 'is_active')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
