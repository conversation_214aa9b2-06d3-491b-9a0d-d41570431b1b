from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import Category, Supplier, Product


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    """Category Admin"""
    list_display = ['name', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name']
    ordering = ['name']


@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    """Supplier Admin"""
    list_display = ['name', 'contact_person', 'phone', 'email', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'contact_person', 'phone', 'email']
    ordering = ['name']


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    """Product Admin"""
    list_display = ['name', 'sku', 'category', 'selling_price', 'stock_quantity', 'is_low_stock', 'is_active']
    list_filter = ['category', 'is_active', 'created_at']
    search_fields = ['name', 'sku']
    ordering = ['name']
    readonly_fields = ['barcode_image', 'created_at', 'updated_at']

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('name', 'sku', 'category', 'description')
        }),
        (_('Pricing'), {
            'fields': ('cost_price', 'selling_price')
        }),
        (_('Inventory'), {
            'fields': ('stock_quantity', 'min_stock_level')
        }),
        (_('Media'), {
            'fields': ('product_image', 'barcode_image')
        }),
        (_('Status'), {
            'fields': ('is_active',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def is_low_stock(self, obj):
        return obj.is_low_stock
    is_low_stock.boolean = True
    is_low_stock.short_description = _('Low Stock')
