{% extends 'base.html' %}
{% load i18n crispy_forms_tags %}

{% block title %}{% trans "Add Category" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <!-- <PERSON> Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">{% trans "Add New Category" %}</h1>
                    <p class="text-muted">{% trans "Create a new product category" %}</p>
                </div>
                <a href="{% url 'products:category_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Categories" %}
                </a>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-tags me-2"></i>{% trans "Category Information" %}
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        {% crispy form %}
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
