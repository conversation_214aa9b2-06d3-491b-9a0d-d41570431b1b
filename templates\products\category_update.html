{% extends 'base.html' %}
{% load i18n crispy_forms_tags %}

{% block title %}{% trans "Edit Category" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">{% trans "Edit Category" %}</h1>
                    <p class="text-muted">{% trans "Update category information" %}</p>
                </div>
                <a href="{% url 'products:category_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Categories" %}
                </a>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-edit me-2"></i>{% trans "Category Information" %}
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        {% crispy form %}
                    </form>
                </div>
            </div>
            
            <!-- Category Statistics -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>{% trans "Category Statistics" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <h4 class="text-primary">{{ object.product_set.count }}</h4>
                            <small class="text-muted">{% trans "Total Products" %}</small>
                        </div>
                        <div class="col-4">
                            <h4 class="text-success">{{ object.product_set.filter.is_active=True.count }}</h4>
                            <small class="text-muted">{% trans "Active Products" %}</small>
                        </div>
                        <div class="col-4">
                            <h4 class="text-muted">{{ object.created_at|date:"M Y" }}</h4>
                            <small class="text-muted">{% trans "Created" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
