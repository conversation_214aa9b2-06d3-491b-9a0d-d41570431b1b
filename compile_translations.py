#!/usr/bin/env python3
"""
Script to compile translation files manually
"""
import os
import sys
import django
from django.conf import settings
from django.core.management import execute_from_command_line

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sales_invoice_system.settings')
django.setup()

# Compile messages
try:
    from django.core.management.commands.compilemessages import Command
    command = Command()
    command.handle(verbosity=1)
    print("Translation files compiled successfully!")
except Exception as e:
    print(f"Error compiling translations: {e}")
    
    # Manual compilation using msgfmt if available
    import subprocess
    try:
        po_file = 'locale/ar/LC_MESSAGES/django.po'
        mo_file = 'locale/ar/LC_MESSAGES/django.mo'
        
        # Try using msgfmt
        result = subprocess.run(['msgfmt', '-o', mo_file, po_file], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("Manual compilation successful!")
        else:
            print("msgfmt not available, creating empty .mo file")
            # Create empty .mo file
            with open(mo_file, 'wb') as f:
                # Write minimal .mo file header
                f.write(b'\xde\x12\x04\x95\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00')
            print("Empty .mo file created")
    except Exception as e2:
        print(f"Manual compilation failed: {e2}")
