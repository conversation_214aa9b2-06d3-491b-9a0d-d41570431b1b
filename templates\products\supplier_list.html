{% extends 'base.html' %}
{% load i18n %}
{% load arabic_trans %}

{% block title %}{% trans "Suppliers" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{% trans "Supplier Management" %}</h1>
            <p class="text-muted">{% trans "Manage your suppliers and vendors" %}</p>
        </div>
        <div class="btn-group" role="group">
            <a href="{% url 'products:supplier_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>{% trans "Add Supplier" %}
            </a>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-cog me-2"></i>{% trans "More" %}
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#">
                        <i class="fas fa-upload me-2"></i>{% trans "Import Suppliers" %}
                    </a></li>
                    <li><a class="dropdown-item" href="#">
                        <i class="fas fa-download me-2"></i>{% trans "Export Suppliers" %}
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <input type="text" name="search" class="form-control" 
                           placeholder="{% trans 'Search suppliers...' %}" 
                           value="{{ request.GET.search }}">
                </div>
                <div class="col-md-3">
                    <select name="status" class="form-control">
                        <option value="">{% trans "All Status" %}</option>
                        <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>{% trans "Active" %}</option>
                        <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>{% trans "Inactive" %}</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select name="country" class="form-control">
                        <option value="">{% trans "All Countries" %}</option>
                        <option value="QA" {% if request.GET.country == 'QA' %}selected{% endif %}>{% trans "Qatar" %}</option>
                        <option value="SA" {% if request.GET.country == 'SA' %}selected{% endif %}>{% trans "Saudi Arabia" %}</option>
                        <option value="AE" {% if request.GET.country == 'AE' %}selected{% endif %}>{% trans "UAE" %}</option>
                        <option value="KW" {% if request.GET.country == 'KW' %}selected{% endif %}>{% trans "Kuwait" %}</option>
                        <option value="BH" {% if request.GET.country == 'BH' %}selected{% endif %}>{% trans "Bahrain" %}</option>
                        <option value="OM" {% if request.GET.country == 'OM' %}selected{% endif %}>{% trans "Oman" %}</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i>
                    </button>
                    <a href="{% url 'products:supplier_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Suppliers Table -->
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-truck me-2"></i>{% trans "Suppliers" %}
                {% if suppliers %}
                    <span class="badge bg-primary ms-2">{{ suppliers.count }}</span>
                {% endif %}
            </h6>
        </div>
        <div class="card-body p-0">
            {% if suppliers %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>{% trans "Supplier" %}</th>
                                <th>{% trans "Contact" %}</th>
                                <th>{% trans "Location" %}</th>
                                <th>{% trans "Products" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for supplier in suppliers %}
                                <tr>
                                    <td>
                                        <div>
                                            <div class="fw-bold">{{ supplier.name }}</div>
                                            {% if supplier.company_registration %}
                                                <small class="text-muted">{% trans "Reg" %}: {{ supplier.company_registration }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            {% if supplier.contact_person %}
                                                <div class="fw-bold">{{ supplier.contact_person }}</div>
                                            {% endif %}
                                            <div>{{ supplier.phone }}</div>
                                            {% if supplier.email %}
                                                <small class="text-muted">{{ supplier.email }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            {% if supplier.city %}{{ supplier.city }}, {% endif %}{{ supplier.country }}
                                        </div>
                                        {% if supplier.address %}
                                            <small class="text-muted">{{ supplier.address|truncatechars:30 }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ supplier.products.count }} {% trans "products" %}</span>
                                    </td>
                                    <td>
                                        {% if supplier.is_active %}
                                            <span class="badge bg-success">{% trans "Active" %}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{% url 'products:supplier_detail' supplier.pk %}" 
                                               class="btn btn-outline-info" title="{% trans 'View Details' %}">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'products:supplier_update' supplier.pk %}" 
                                               class="btn btn-outline-primary" title="{% trans 'Edit Supplier' %}">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'products:supplier_delete' supplier.pk %}" 
                                               class="btn btn-outline-danger" title="{% trans 'Delete Supplier' %}"
                                               onclick="return confirm('{% trans 'Are you sure you want to delete this supplier?' %}')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-truck text-muted" style="font-size: 3rem;"></i>
                    <h5 class="mt-3 text-muted">{% trans "No suppliers found" %}</h5>
                    <p class="text-muted">{% trans "Add your first supplier to get started." %}</p>
                    <a href="{% url 'products:supplier_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "Add First Supplier" %}
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
