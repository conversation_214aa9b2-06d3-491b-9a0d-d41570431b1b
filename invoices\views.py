from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import CreateView, UpdateView, ListView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q, Sum, F
from django.utils import timezone
from django.db import transaction
from django.template.loader import render_to_string
from django.conf import settings
import json
from decimal import Decimal
from datetime import datetime, timedelta
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from io import BytesIO
from .models import Invoice, InvoiceItem, Payment
from .forms import InvoiceForm, InvoiceItemFormSet, PaymentForm, InvoiceSearchForm, QuickInvoiceForm
from customers.models import Customer
from products.models import Product
from accounts.models import User


def can_manage_invoices(user):
    """Check if user can manage invoices"""
    return user.is_authenticated and user.role in [User.UserRole.ADMIN, User.UserRole.SALES_EMPLOYEE]


def can_view_invoices(user):
    """Check if user can view invoices"""
    return user.is_authenticated


class InvoiceManagementMixin(UserPassesTestMixin):
    """Mixin to require invoice management permissions"""
    def test_func(self):
        return can_manage_invoices(self.request.user)


@login_required
def invoice_list(request):
    """List all invoices with search and filtering"""
    form = InvoiceSearchForm(request.GET)
    invoices = Invoice.objects.select_related('customer', 'created_by').order_by('-created_at')

    if form.is_valid():
        search = form.cleaned_data.get('search')
        customer = form.cleaned_data.get('customer')
        status = form.cleaned_data.get('status')
        payment_method = form.cleaned_data.get('payment_method')
        date_from = form.cleaned_data.get('date_from')
        date_to = form.cleaned_data.get('date_to')

        if search:
            invoices = invoices.filter(
                Q(invoice_number__icontains=search) |
                Q(customer__name__icontains=search)
            )

        if customer:
            invoices = invoices.filter(customer=customer)

        if status:
            invoices = invoices.filter(status=status)

        if payment_method:
            invoices = invoices.filter(payment_method=payment_method)

        if date_from:
            invoices = invoices.filter(invoice_date__gte=date_from)

        if date_to:
            invoices = invoices.filter(invoice_date__lte=date_to)

    # Pagination
    paginator = Paginator(invoices, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'invoices': page_obj,
        'form': form,
        'is_paginated': page_obj.has_other_pages(),
        'page_obj': page_obj,
    }

    return render(request, 'invoices/invoice_list.html', context)


@login_required
@user_passes_test(can_manage_invoices)
def invoice_create(request):
    """Create new invoice with line items"""
    if request.method == 'POST':
        form = InvoiceForm(request.POST)
        formset = InvoiceItemFormSet(request.POST)

        if form.is_valid() and formset.is_valid():
            with transaction.atomic():
                invoice = form.save(commit=False)
                invoice.created_by = request.user
                invoice.save()

                formset.instance = invoice
                items = formset.save()

                # Update product stock
                for item in items:
                    product = item.product
                    product.stock_quantity -= item.quantity
                    product.save()

                # Calculate totals
                invoice.calculate_totals()
                invoice.save()

                messages.success(request, _('Invoice created successfully.'))
                return redirect('invoices:invoice_detail', pk=invoice.pk)
    else:
        form = InvoiceForm()
        formset = InvoiceItemFormSet()

    context = {
        'form': form,
        'formset': formset,
        'products': Product.objects.filter(is_active=True).values('id', 'name', 'sku', 'selling_price', 'stock_quantity'),
    }

    return render(request, 'invoices/invoice_create.html', context)


@login_required
def invoice_detail(request, pk):
    """Invoice detail view"""
    invoice = get_object_or_404(Invoice, pk=pk)

    # Check permissions
    if not can_view_invoices(request.user):
        messages.error(request, _('You do not have permission to view this invoice.'))
        return redirect('dashboard:home')

    # Get payments
    payments = invoice.payments.order_by('-payment_date')

    context = {
        'invoice': invoice,
        'payments': payments,
        'can_edit': can_manage_invoices(request.user),
    }

    return render(request, 'invoices/invoice_detail.html', context)


@login_required
@user_passes_test(can_manage_invoices)
def invoice_update(request, pk):
    """Update existing invoice"""
    invoice = get_object_or_404(Invoice, pk=pk)

    # Check if invoice can be edited
    if invoice.status in ['paid', 'cancelled']:
        messages.error(request, _('Cannot edit a {} invoice.').format(invoice.get_status_display().lower()))
        return redirect('invoices:invoice_detail', pk=pk)

    if request.method == 'POST':
        form = InvoiceForm(request.POST, instance=invoice)
        formset = InvoiceItemFormSet(request.POST, instance=invoice)

        if form.is_valid() and formset.is_valid():
            with transaction.atomic():
                # Restore stock for existing items
                for item in invoice.items.all():
                    product = item.product
                    product.stock_quantity += item.quantity
                    product.save()

                # Save updated invoice
                invoice = form.save()
                items = formset.save()

                # Update stock for new/updated items
                for item in items:
                    product = item.product
                    product.stock_quantity -= item.quantity
                    product.save()

                # Recalculate totals
                invoice.calculate_totals()
                invoice.save()

                messages.success(request, _('Invoice updated successfully.'))
                return redirect('invoices:invoice_detail', pk=invoice.pk)
    else:
        form = InvoiceForm(instance=invoice)
        formset = InvoiceItemFormSet(instance=invoice)

    context = {
        'form': form,
        'formset': formset,
        'invoice': invoice,
        'products': Product.objects.filter(is_active=True).values('id', 'name', 'sku', 'selling_price', 'stock_quantity'),
    }

    return render(request, 'invoices/invoice_update.html', context)


@login_required
@user_passes_test(can_manage_invoices)
def invoice_delete(request, pk):
    """Delete invoice (only if draft)"""
    invoice = get_object_or_404(Invoice, pk=pk)

    if invoice.status != 'draft':
        messages.error(request, _('Only draft invoices can be deleted.'))
        return redirect('invoices:invoice_detail', pk=pk)

    if request.method == 'POST':
        # Restore stock
        for item in invoice.items.all():
            product = item.product
            product.stock_quantity += item.quantity
            product.save()

        invoice.delete()
        messages.success(request, _('Invoice deleted successfully.'))
        return redirect('invoices:invoice_list')

    return render(request, 'invoices/invoice_confirm_delete.html', {'invoice': invoice})


@login_required
@user_passes_test(can_manage_invoices)
def record_payment(request, pk):
    """Record payment for invoice"""
    invoice = get_object_or_404(Invoice, pk=pk)

    if request.method == 'POST':
        form = PaymentForm(request.POST, invoice=invoice)
        if form.is_valid():
            payment = form.save(commit=False)
            payment.invoice = invoice
            payment.created_by = request.user
            payment.status = 'completed'
            payment.save()

            # Update invoice status
            total_payments = invoice.payments.filter(status='completed').aggregate(
                total=Sum('amount')
            )['total'] or 0

            if total_payments >= invoice.total_amount:
                invoice.status = 'paid'
                invoice.save()

            messages.success(request, _('Payment recorded successfully.'))
            return redirect('invoices:invoice_detail', pk=pk)
    else:
        form = PaymentForm(invoice=invoice)

    context = {
        'form': form,
        'invoice': invoice,
    }

    return render(request, 'invoices/record_payment.html', context)


@login_required
def invoice_pdf(request, pk):
    """Generate PDF for invoice"""
    invoice = get_object_or_404(Invoice, pk=pk)

    # Check permissions
    if not can_view_invoices(request.user):
        messages.error(request, _('You do not have permission to view this invoice.'))
        return redirect('dashboard:home')

    # Create PDF
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="invoice_{invoice.invoice_number}.pdf"'

    # Create PDF document
    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)

    # Get styles
    styles = getSampleStyleSheet()

    # Build PDF content
    story = []

    # Company header
    company_info = Paragraph(f"""
        <b>{settings.COMPANY_NAME}</b><br/>
        {settings.COMPANY_ADDRESS}<br/>
        Phone: {settings.COMPANY_PHONE}<br/>
        Email: {settings.COMPANY_EMAIL}<br/>
        Tax ID: {settings.COMPANY_TAX_ID}
    """, styles['Normal'])
    story.append(company_info)
    story.append(Spacer(1, 20))

    # Invoice header
    invoice_header = Paragraph(f"""
        <b>INVOICE #{invoice.invoice_number}</b><br/>
        Date: {invoice.invoice_date}<br/>
        Due Date: {invoice.due_date}
    """, styles['Heading2'])
    story.append(invoice_header)
    story.append(Spacer(1, 20))

    # Customer info
    customer_info = Paragraph(f"""
        <b>Bill To:</b><br/>
        {invoice.customer.name}<br/>
        {invoice.customer.company_name or ''}<br/>
        {invoice.customer.address or ''}<br/>
        Phone: {invoice.customer.phone}<br/>
        {invoice.customer.email or ''}
    """, styles['Normal'])
    story.append(customer_info)
    story.append(Spacer(1, 20))

    # Invoice items table
    data = [['Product', 'Quantity', 'Unit Price', 'Total']]
    for item in invoice.items.all():
        data.append([
            item.product.name,
            str(item.quantity),
            f'${item.unit_price}',
            f'${item.line_total}'
        ])

    # Add totals
    data.append(['', '', 'Subtotal:', f'${invoice.subtotal}'])
    if invoice.discount_amount > 0:
        data.append(['', '', f'Discount ({invoice.discount_percentage}%):', f'-${invoice.discount_amount}'])
    if invoice.tax_amount > 0:
        data.append(['', '', f'Tax ({invoice.tax_rate}%):', f'${invoice.tax_amount}'])
    data.append(['', '', 'Total:', f'${invoice.total_amount}'])

    table = Table(data)
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 14),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    story.append(table)

    # Notes
    if invoice.notes:
        story.append(Spacer(1, 20))
        notes = Paragraph(f"<b>Notes:</b><br/>{invoice.notes}", styles['Normal'])
        story.append(notes)

    # Terms and conditions
    if invoice.terms_and_conditions:
        story.append(Spacer(1, 20))
        terms = Paragraph(f"<b>Terms and Conditions:</b><br/>{invoice.terms_and_conditions}", styles['Normal'])
        story.append(terms)

    # Build PDF
    doc.build(story)

    # Get PDF data
    pdf_data = buffer.getvalue()
    buffer.close()

    response.write(pdf_data)
    return response


@login_required
@user_passes_test(can_manage_invoices)
def update_invoice_status(request, pk):
    """Update invoice status"""
    if request.method == 'POST':
        invoice = get_object_or_404(Invoice, pk=pk)
        new_status = request.POST.get('status')

        if new_status in dict(Invoice.InvoiceStatus.choices):
            invoice.status = new_status
            invoice.save()

            messages.success(request, _('Invoice status updated successfully.'))

            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': True,
                    'status': invoice.get_status_display(),
                    'message': str(messages.get_messages(request)[-1])
                })

    return redirect('invoices:invoice_detail', pk=pk)


@login_required
@user_passes_test(can_manage_invoices)
def quick_invoice(request):
    """Quick invoice creation for POS-style interface"""
    if request.method == 'POST':
        data = json.loads(request.body)

        try:
            with transaction.atomic():
                # Create invoice
                invoice = Invoice.objects.create(
                    customer_id=data['customer_id'],
                    payment_method=data['payment_method'],
                    invoice_date=timezone.now().date(),
                    due_date=timezone.now().date() + timedelta(days=30),
                    created_by=request.user,
                    tax_rate=data.get('tax_rate', 0),
                    discount_percentage=data.get('discount_percentage', 0)
                )

                # Create invoice items
                for item_data in data['items']:
                    product = Product.objects.get(id=item_data['product_id'])
                    quantity = int(item_data['quantity'])

                    # Check stock
                    if quantity > product.stock_quantity:
                        raise ValueError(f'Insufficient stock for {product.name}')

                    # Create item
                    InvoiceItem.objects.create(
                        invoice=invoice,
                        product=product,
                        quantity=quantity,
                        unit_price=item_data.get('unit_price', product.selling_price)
                    )

                    # Update stock
                    product.stock_quantity -= quantity
                    product.save()

                # Calculate totals
                invoice.calculate_totals()
                invoice.save()

                return JsonResponse({
                    'success': True,
                    'invoice_id': invoice.id,
                    'invoice_number': invoice.invoice_number,
                    'total_amount': str(invoice.total_amount)
                })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            })

    # GET request - show quick invoice form
    form = QuickInvoiceForm()
    products = Product.objects.filter(is_active=True, stock_quantity__gt=0)

    context = {
        'form': form,
        'products': products,
    }

    return render(request, 'invoices/quick_invoice.html', context)


@login_required
def get_product_info(request, product_id):
    """Get product information for AJAX requests"""
    try:
        product = Product.objects.get(id=product_id, is_active=True)
        return JsonResponse({
            'success': True,
            'product': {
                'id': product.id,
                'name': product.name,
                'sku': product.sku,
                'selling_price': str(product.selling_price),
                'stock_quantity': product.stock_quantity,
                'is_low_stock': product.is_low_stock
            }
        })
    except Product.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': _('Product not found')
        })


@login_required
def overdue_invoices(request):
    """View overdue invoices (accessible to accountants and admins)"""
    if not (request.user.role in [User.UserRole.ADMIN, User.UserRole.ACCOUNTANT]):
        messages.error(request, _('You do not have permission to view this page.'))
        return redirect('dashboard:home')

    # Get overdue invoices
    today = timezone.now().date()
    overdue_invoices = Invoice.objects.filter(
        due_date__lt=today,
        status__in=['pending', 'overdue']
    ).select_related('customer', 'created_by').order_by('due_date')

    # Update status to overdue
    overdue_invoices.update(status='overdue')

    context = {
        'invoices': overdue_invoices,
        'total_overdue': overdue_invoices.aggregate(total=Sum('total_amount'))['total'] or 0,
    }

    return render(request, 'invoices/overdue_invoices.html', context)
