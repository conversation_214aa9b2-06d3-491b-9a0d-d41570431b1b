from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _


class User(AbstractUser):
    """
    Custom User model with role-based access control
    Supports Admin, Sales Employee, and Accountant roles
    """

    class UserRole(models.TextChoices):
        ADMIN = 'admin', _('Admin')
        SALES_EMPLOYEE = 'sales_employee', _('Sales Employee')
        ACCOUNTANT = 'accountant', _('Accountant')

    role = models.CharField(
        max_length=20,
        choices=UserRole.choices,
        default=UserRole.SALES_EMPLOYEE,
        verbose_name=_('Role')
    )

    phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('Phone Number')
    )

    address = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Address')
    )

    profile_image = models.ImageField(
        upload_to='profiles/',
        blank=True,
        null=True,
        verbose_name=_('Profile Image')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Active Status')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )

    class Meta:
        verbose_name = _('User')
        verbose_name_plural = _('Users')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.username} ({self.get_role_display()})"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}".strip()

    def has_permission(self, permission):
        """Check if user has specific permission based on role"""
        permissions = {
            self.UserRole.ADMIN: [
                'view_all', 'create_all', 'edit_all', 'delete_all',
                'manage_users', 'view_reports', 'system_settings'
            ],
            self.UserRole.SALES_EMPLOYEE: [
                'view_products', 'create_invoice', 'edit_invoice',
                'view_customers', 'create_customer', 'edit_customer'
            ],
            self.UserRole.ACCOUNTANT: [
                'view_all', 'view_reports', 'manage_payments',
                'view_financial_data', 'export_reports'
            ]
        }
        return permission in permissions.get(self.role, [])
