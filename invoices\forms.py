from django import forms
from django.utils.translation import gettext_lazy as _
from django.forms import inlineformset_factory
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Row, Column, Submit, Field, HTML, Div
from crispy_forms.bootstrap import FormActions
from decimal import Decimal
from .models import Invoice, InvoiceItem, Payment
from customers.models import Customer
from products.models import Product


class InvoiceForm(forms.ModelForm):
    """Form for creating and editing invoices"""
    
    class Meta:
        model = Invoice
        fields = [
            'customer', 'invoice_date', 'due_date', 'payment_method',
            'tax_rate', 'discount_percentage', 'notes', 'terms_and_conditions'
        ]
        widgets = {
            'customer': forms.Select(attrs={'class': 'form-control'}),
            'invoice_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'due_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'payment_method': forms.Select(attrs={'class': 'form-control'}),
            'tax_rate': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
            'discount_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'terms_and_conditions': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['customer'].queryset = Customer.objects.filter(is_active=True)
        
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('customer', css_class='form-group col-md-6 mb-3'),
                Column('payment_method', css_class='form-group col-md-6 mb-3'),
            ),
            Row(
                Column('invoice_date', css_class='form-group col-md-6 mb-3'),
                Column('due_date', css_class='form-group col-md-6 mb-3'),
            ),
            Row(
                Column('tax_rate', css_class='form-group col-md-6 mb-3'),
                Column('discount_percentage', css_class='form-group col-md-6 mb-3'),
            ),
            Field('notes', css_class='mb-3'),
            Field('terms_and_conditions', css_class='mb-3'),
        )
    
    def clean(self):
        cleaned_data = super().clean()
        invoice_date = cleaned_data.get('invoice_date')
        due_date = cleaned_data.get('due_date')
        
        if invoice_date and due_date and due_date < invoice_date:
            raise forms.ValidationError(_('Due date cannot be before invoice date.'))
        
        return cleaned_data


class InvoiceItemForm(forms.ModelForm):
    """Form for invoice line items"""
    
    class Meta:
        model = InvoiceItem
        fields = ['product', 'quantity', 'unit_price']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-control product-select'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control quantity-input', 'min': '1'}),
            'unit_price': forms.NumberInput(attrs={'class': 'form-control price-input', 'step': '0.01', 'min': '0'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['product'].queryset = Product.objects.filter(is_active=True)
        self.fields['product'].empty_label = _('Select a product')
    
    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        product = self.cleaned_data.get('product')
        
        if quantity and product:
            if quantity > product.stock_quantity:
                raise forms.ValidationError(
                    _('Quantity ({}) exceeds available stock ({})').format(quantity, product.stock_quantity)
                )
        
        return quantity


# Create formset for invoice items
InvoiceItemFormSet = inlineformset_factory(
    Invoice,
    InvoiceItem,
    form=InvoiceItemForm,
    extra=1,
    min_num=1,
    validate_min=True,
    can_delete=True
)


class PaymentForm(forms.ModelForm):
    """Form for recording payments"""
    
    class Meta:
        model = Payment
        fields = ['amount', 'payment_method', 'payment_date', 'reference_number', 'notes']
        widgets = {
            'amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0.01'}),
            'payment_method': forms.Select(attrs={'class': 'form-control'}),
            'payment_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'reference_number': forms.TextInput(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        self.invoice = kwargs.pop('invoice', None)
        super().__init__(*args, **kwargs)
        
        if self.invoice:
            # Set maximum payment amount to outstanding balance
            outstanding = self.invoice.total_amount - sum(
                p.amount for p in self.invoice.payments.filter(status='completed')
            )
            self.fields['amount'].widget.attrs['max'] = str(outstanding)
            self.fields['amount'].help_text = _('Maximum: ${}').format(outstanding)
        
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('amount', css_class='form-group col-md-6 mb-3'),
                Column('payment_method', css_class='form-group col-md-6 mb-3'),
            ),
            Row(
                Column('payment_date', css_class='form-group col-md-6 mb-3'),
                Column('reference_number', css_class='form-group col-md-6 mb-3'),
            ),
            Field('notes', css_class='mb-3'),
            Submit('submit', _('Record Payment'), css_class='btn btn-success')
        )
    
    def clean_amount(self):
        amount = self.cleaned_data.get('amount')
        
        if self.invoice and amount:
            outstanding = self.invoice.total_amount - sum(
                p.amount for p in self.invoice.payments.filter(status='completed')
            )
            
            if amount > outstanding:
                raise forms.ValidationError(
                    _('Payment amount cannot exceed outstanding balance of ${}').format(outstanding)
                )
        
        return amount


class InvoiceSearchForm(forms.Form):
    """Form for searching and filtering invoices"""
    
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Search by invoice number or customer name')
        })
    )
    
    customer = forms.ModelChoiceField(
        queryset=Customer.objects.filter(is_active=True),
        required=False,
        empty_label=_('All Customers'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    status = forms.ChoiceField(
        choices=[('', _('All Status'))] + Invoice.InvoiceStatus.choices,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    payment_method = forms.ChoiceField(
        choices=[('', _('All Payment Methods'))] + Invoice.PaymentMethod.choices,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )


class QuickInvoiceForm(forms.Form):
    """Quick invoice form for POS-style interface"""
    
    customer = forms.ModelChoiceField(
        queryset=Customer.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Customer')
    )
    
    payment_method = forms.ChoiceField(
        choices=Invoice.PaymentMethod.choices,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Payment Method')
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('customer', css_class='form-group col-md-6 mb-3'),
                Column('payment_method', css_class='form-group col-md-6 mb-3'),
            ),
        )
