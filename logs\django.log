Watching for file changes with StatReloader
"GET / HTTP/1.1" 302 0
"GET / HTTP/1.1" 302 0
"GET /dashboard/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/dashboard/ HTTP/1.1" 200 4890
"GET /static/js/main.js HTTP/1.1" 200 7680
"GET /static/css/style.css HTTP/1.1" 200 4442
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 3132
"POST /accounts/login/?next=/dashboard/ HTTP/1.1" 302 0
"GET /dashboard/ HTTP/1.1" 200 16561
"GET /accounts/profile/ HTTP/1.1" 200 12984
"GET /accounts/change-password/ HTTP/1.1" 200 11563
"GET /accounts/profile/ HTTP/1.1" 200 12984
"GET /dashboard/ HTTP/1.1" 200 16103
E:\Visual Studio Code\075\products\views.py changed, reloading.
Watching for file changes with StatReloader
E:\Visual Studio Code\075\products\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
E:\Visual Studio Code\075\products\views.py changed, reloading.
Watching for file changes with StatReloader
E:\Visual Studio Code\075\products\urls.py changed, reloading.
Watching for file changes with StatReloader
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /accounts/profile/ HTTP/1.1" 200 12984
"GET /accounts/change-password/ HTTP/1.1" 200 11563
"GET /accounts/profile/ HTTP/1.1" 200 12984
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /accounts/profile/ HTTP/1.1" 200 12984
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /dashboard/ HTTP/1.1" 200 16103
Watching for file changes with StatReloader
"GET / HTTP/1.1" 302 0
"GET /dashboard/ HTTP/1.1" 200 16400
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /invoices/create/ HTTP/1.1" 200 25364
"GET /products/create/ HTTP/1.1" 200 14826
"GET /products/categories/ HTTP/1.1" 200 8459
"GET /products/categories/create/ HTTP/1.1" 200 9666
Internal Server Error: /products/suppliers/
Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: products/supplier_list.html, products/supplier_list.html
"GET /products/suppliers/ HTTP/1.1" 500 93162
"GET /customers/ HTTP/1.1" 200 11216
"GET /customers/create/ HTTP/1.1" 200 15415
"GET /invoices/ HTTP/1.1" 200 11686
"GET /invoices/create/ HTTP/1.1" 200 25364
"GET /invoices/quick/ HTTP/1.1" 200 21960
"GET /customers/ HTTP/1.1" 200 11216
"GET /customers/create/ HTTP/1.1" 200 15415
"GET /customers/ HTTP/1.1" 200 11216
"GET /products/ HTTP/1.1" 200 11639
"GET /products/create/ HTTP/1.1" 200 14826
"GET /products/categories/ HTTP/1.1" 200 8459
Internal Server Error: /products/suppliers/
Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: products/supplier_list.html, products/supplier_list.html
"GET /products/suppliers/ HTTP/1.1" 500 93155
"GET /dashboard/ HTTP/1.1" 200 16400
"GET /products/create/ HTTP/1.1" 200 15013
"GET /invoices/create/ HTTP/1.1" 200 28956
Internal Server Error: /products/suppliers/
Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: products/supplier_list.html, products/supplier_list.html
"GET /products/suppliers/ HTTP/1.1" 500 93151
"GET /products/categories/ HTTP/1.1" 200 18619
"GET /dashboard/ HTTP/1.1" 200 16400
"GET /products/ HTTP/1.1" 200 66141
"GET /reports/ HTTP/1.1" 200 16906
"GET /dashboard/ HTTP/1.1" 200 16400
"GET /invoices/create/ HTTP/1.1" 200 28956
"GET /products/ HTTP/1.1" 200 66141
"GET /products/14/ HTTP/1.1" 200 15395
"GET /media/barcodes/RACKET-T_barcode.png HTTP/1.1" 200 3649
"GET /dashboard/ HTTP/1.1" 200 16400
Internal Server Error: /products/suppliers/
Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: products/supplier_list.html, products/supplier_list.html
"GET /products/suppliers/ HTTP/1.1" 500 93145
"GET /products/ HTTP/1.1" 200 66141
"GET /products/create/ HTTP/1.1" 200 15013
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /products/ HTTP/1.1" 200 66141
"GET /static/css/style.css HTTP/1.1" 304 0
E:\Visual Studio Code\075\products\urls.py changed, reloading.
Watching for file changes with StatReloader
E:\Visual Studio Code\075\products\views.py changed, reloading.
Watching for file changes with StatReloader
E:\Visual Studio Code\075\products\views.py changed, reloading.
Watching for file changes with StatReloader
E:\Visual Studio Code\075\products\views.py changed, reloading.
Watching for file changes with StatReloader
E:\Visual Studio Code\075\products\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /products/suppliers/ HTTP/1.1" 200 10847
"GET /static/js/main.js HTTP/1.1" 304 0
Internal Server Error: /products/suppliers/create/
Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\defaulttags.py", line 1026, in find_library
    return parser.libraries[name]
           ~~~~~~~~~~~~~~~~^^^^^^
KeyError: 'widget_tweaks'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader.py", line 42, in select_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\defaulttags.py", line 1088, in load
    lib = find_library(parser, name)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\defaulttags.py", line 1028, in find_library
    raise TemplateSyntaxError(
    ...<5 lines>...
    )
django.template.exceptions.TemplateSyntaxError: 'widget_tweaks' is not a registered tag library. Must be one of:
account
admin_list
admin_modify
admin_urls
cache
crispy_forms_field
crispy_forms_filters
crispy_forms_tags
crispy_forms_utils
debugger_tags
highlighting
i18n
indent_text
l10n
log
rest_framework
socialaccount
static
syntax_color
tz
widont
"GET /products/suppliers/create/ HTTP/1.1" **********
"GET /products/create/ HTTP/1.1" 200 15013
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /products/categories/ HTTP/1.1" 200 18619
"GET /products/categories/create/ HTTP/1.1" 200 9666
"GET /products/suppliers/ HTTP/1.1" 200 10847
Internal Server Error: /products/suppliers/create/
Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\defaulttags.py", line 1026, in find_library
    return parser.libraries[name]
           ~~~~~~~~~~~~~~~~^^^^^^
KeyError: 'widget_tweaks'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader.py", line 42, in select_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\defaulttags.py", line 1088, in load
    lib = find_library(parser, name)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\defaulttags.py", line 1028, in find_library
    raise TemplateSyntaxError(
    ...<5 lines>...
    )
django.template.exceptions.TemplateSyntaxError: 'widget_tweaks' is not a registered tag library. Must be one of:
account
admin_list
admin_modify
admin_urls
cache
crispy_forms_field
crispy_forms_filters
crispy_forms_tags
crispy_forms_utils
debugger_tags
highlighting
i18n
indent_text
l10n
log
rest_framework
socialaccount
static
syntax_color
tz
widont
"GET /products/suppliers/create/ HTTP/1.1" **********
E:\Visual Studio Code\075\sales_invoice_system\settings.py changed, reloading.
E:\Visual Studio Code\075\sales_invoice_system\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
E:\Visual Studio Code\075\sales_invoice_system\settings.py changed, reloading.
E:\Visual Studio Code\075\sales_invoice_system\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /products/suppliers/ HTTP/1.1" 200 10847
"GET /products/suppliers/ HTTP/1.1" 200 10847
"GET /products/suppliers/ HTTP/1.1" 200 10847
"GET /products/suppliers/ HTTP/1.1" 200 10847
"GET /dashboard/ HTTP/1.1" 200 16400
"GET /dashboard/ HTTP/1.1" 200 16400
"GET /dashboard/ HTTP/1.1" 200 16400
"GET /dashboard/ HTTP/1.1" 200 16400
"GET /invoices/create/ HTTP/1.1" 200 28956
"GET /customers/create/ HTTP/1.1" 200 15415
"GET /customers/ HTTP/1.1" 200 34904
Internal Server Error: /customers/8/edit/
Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: customers/customer_update.html
"GET /customers/8/edit/ HTTP/1.1" 500 89867
Internal Server Error: /customers/8/
Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "E:\Visual Studio Code\075\customers\views.py", line 137, in customer_detail
    return render(request, 'customers/customer_detail.html', context)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\defaulttags.py", line 1482, in do_with
    extra_context = token_kwargs(remaining_bits, parser, support_legacy=True)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 1116, in token_kwargs
    kwargs[key] = parser.compile_filter(value)
                  ~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 600, in compile_filter
    return FilterExpression(token, self)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'div'
"GET /customers/8/ HTTP/1.1" **********
Internal Server Error: /customers/1/
Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "E:\Visual Studio Code\075\customers\views.py", line 137, in customer_detail
    return render(request, 'customers/customer_detail.html', context)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\defaulttags.py", line 1482, in do_with
    extra_context = token_kwargs(remaining_bits, parser, support_legacy=True)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 1116, in token_kwargs
    kwargs[key] = parser.compile_filter(value)
                  ~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 600, in compile_filter
    return FilterExpression(token, self)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'div'
"GET /customers/1/ HTTP/1.1" **********
Internal Server Error: /customers/8/
Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "E:\Visual Studio Code\075\customers\views.py", line 137, in customer_detail
    return render(request, 'customers/customer_detail.html', context)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\defaulttags.py", line 1482, in do_with
    extra_context = token_kwargs(remaining_bits, parser, support_legacy=True)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 1116, in token_kwargs
    kwargs[key] = parser.compile_filter(value)
                  ~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 600, in compile_filter
    return FilterExpression(token, self)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'div'
"GET /customers/8/ HTTP/1.1" **********
E:\Visual Studio Code\075\sales_invoice_system\urls.py changed, reloading.
E:\Visual Studio Code\075\sales_invoice_system\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Internal Server Error: /customers/8/edit/
Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: customers/customer_update.html
"GET /customers/8/edit/ HTTP/1.1" 500 89946
"GET /customers/ HTTP/1.1" 200 36826
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /static/css/style.css HTTP/1.1" 200 6044
"POST /i18n/setlang/ HTTP/1.1" 302 0
"GET /en/customers/ HTTP/1.1" 200 36929
"GET /products/suppliers/ HTTP/1.1" 200 12961
"POST /i18n/setlang/ HTTP/1.1" 302 0
"GET /products/suppliers/ HTTP/1.1" 200 12961
"GET /dashboard/ HTTP/1.1" 200 18332
Internal Server Error: /products/suppliers/create/
Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\defaulttags.py", line 1026, in find_library
    return parser.libraries[name]
           ~~~~~~~~~~~~~~~~^^^^^^
KeyError: 'widget_tweaks'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader.py", line 42, in select_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\defaulttags.py", line 1088, in load
    lib = find_library(parser, name)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\defaulttags.py", line 1028, in find_library
    raise TemplateSyntaxError(
    ...<5 lines>...
    )
django.template.exceptions.TemplateSyntaxError: 'widget_tweaks' is not a registered tag library. Must be one of:
account
admin_list
admin_modify
admin_urls
cache
crispy_forms_field
crispy_forms_filters
crispy_forms_tags
crispy_forms_utils
debugger_tags
highlighting
i18n
indent_text
l10n
log
rest_framework
socialaccount
static
syntax_color
tz
widont
"GET /products/suppliers/create/ HTTP/1.1" **********
"GET /customers/create/ HTTP/1.1" 200 17359
"GET /dashboard/ HTTP/1.1" 200 18332
"GET /invoices/create/ HTTP/1.1" 200 30886
"GET /dashboard/ HTTP/1.1" 200 18332
"POST /i18n/setlang/ HTTP/1.1" 302 0
"GET /en/dashboard/ HTTP/1.1" 200 18353
"POST /i18n/setlang/ HTTP/1.1" 302 0
"GET /en/dashboard/ HTTP/1.1" 200 18353
"GET /static/css/style.css HTTP/1.1" 304 0
"POST /i18n/setlang/ HTTP/1.1" 302 0
"GET /en/dashboard/ HTTP/1.1" 200 18353
"POST /i18n/setlang/ HTTP/1.1" 302 0
"GET /en/dashboard/ HTTP/1.1" 200 18353
"POST /i18n/setlang/ HTTP/1.1" 302 0
"GET /en/dashboard/ HTTP/1.1" 200 18353
"GET /en/dashboard/ HTTP/1.1" 200 18353
"GET /en/dashboard/ HTTP/1.1" 200 18353
"POST /i18n/setlang/ HTTP/1.1" 302 0
"GET /en/customers/ HTTP/1.1" 200 36929
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /en/customers/ HTTP/1.1" 200 36929
"GET /en/customers/ HTTP/1.1" 200 36929
"GET /en/customers/ HTTP/1.1" 200 36929
"GET /en/customers/ HTTP/1.1" 200 36929
"GET /en/customers/ HTTP/1.1" 200 36929
"GET /en/dashboard/ HTTP/1.1" 200 18353
"POST /i18n/setlang/ HTTP/1.1" 302 0
"GET /en/dashboard/ HTTP/1.1" 200 18353
"POST /i18n/setlang/ HTTP/1.1" 302 0
"GET /en/dashboard/ HTTP/1.1" 200 18353
"POST /i18n/setlang/ HTTP/1.1" 302 0
"GET /en/dashboard/ HTTP/1.1" 200 18353
E:\Visual Studio Code\075\sales_invoice_system\settings.py changed, reloading.
Watching for file changes with StatReloader
E:\Visual Studio Code\075\sales_invoice_system\urls.py changed, reloading.
Watching for file changes with StatReloader
E:\Visual Studio Code\075\sales_invoice_system\settings.py changed, reloading.
Watching for file changes with StatReloader
E:\Visual Studio Code\075\sales_invoice_system\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"POST /i18n/setlang/ HTTP/1.1" 302 0
"GET /en/dashboard/ HTTP/1.1" 200 18346
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
Internal Server Error: /set_language/
Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\views\decorators\http.py", line 43, in inner
    return func(request, *args, **kwargs)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "E:\Visual Studio Code\075\dashboard\language_views.py", line 23, in set_language
    request.session[translation.LANGUAGE_SESSION_KEY] = language
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: module 'django.utils.translation' has no attribute 'LANGUAGE_SESSION_KEY'
"POST /set_language/ HTTP/1.1" 500 82979
Internal Server Error: /set_language/
Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\views\decorators\http.py", line 43, in inner
    return func(request, *args, **kwargs)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "E:\Visual Studio Code\075\dashboard\language_views.py", line 23, in set_language
    request.session[translation.LANGUAGE_SESSION_KEY] = language
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: module 'django.utils.translation' has no attribute 'LANGUAGE_SESSION_KEY'
"POST /set_language/ HTTP/1.1" 500 82979
E:\Visual Studio Code\075\dashboard\language_views.py changed, reloading.
Watching for file changes with StatReloader
"GET /products/suppliers/ HTTP/1.1" 200 13343
"GET /dashboard/ HTTP/1.1" 200 18472
"GET /products/suppliers/create/ HTTP/1.1" 200 19967
"GET /dashboard/ HTTP/1.1" 200 18472
"GET /dashboard/ HTTP/1.1" 200 18472
"GET /products/ HTTP/1.1" 200 68201
"GET /products/14/ HTTP/1.1" 200 17465
"GET /media/barcodes/RACKET-T_barcode.png HTTP/1.1" 200 3649
"GET /products/14/edit/ HTTP/1.1" 200 17612
"GET /invoices/create/ HTTP/1.1" 200 31026
"GET /dashboard/ HTTP/1.1" 200 18472
"GET /invoices/ HTTP/1.1" 200 73081
"GET /invoices/22/ HTTP/1.1" 200 22138
"GET /invoices/22/pdf/ HTTP/1.1" 200 2763
"GET /customers/create/ HTTP/1.1" 200 17499
"GET /dashboard/ HTTP/1.1" 200 18472
"GET /accounts/profile/ HTTP/1.1" 200 15381
"GET /accounts/change-password/ HTTP/1.1" 200 13988
"GET /dashboard/ HTTP/1.1" 200 18472
"GET /static/css/style.css HTTP/1.1" 304 0
E:\Visual Studio Code\075\dashboard\translations.py changed, reloading.
Watching for file changes with StatReloader
