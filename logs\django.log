Watching for file changes with StatReloader
"GET / HTTP/1.1" 302 0
"GET / HTTP/1.1" 302 0
"GET /dashboard/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/dashboard/ HTTP/1.1" 200 4890
"GET /static/js/main.js HTTP/1.1" 200 7680
"GET /static/css/style.css HTTP/1.1" 200 4442
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 3132
"POST /accounts/login/?next=/dashboard/ HTTP/1.1" 302 0
"GET /dashboard/ HTTP/1.1" 200 16561
"GET /accounts/profile/ HTTP/1.1" 200 12984
"GET /accounts/change-password/ HTTP/1.1" 200 11563
"GET /accounts/profile/ HTTP/1.1" 200 12984
"GET /dashboard/ HTTP/1.1" 200 16103
E:\Visual Studio Code\075\products\views.py changed, reloading.
Watching for file changes with StatReloader
E:\Visual Studio Code\075\products\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
E:\Visual Studio Code\075\products\views.py changed, reloading.
Watching for file changes with StatReloader
E:\Visual Studio Code\075\products\urls.py changed, reloading.
Watching for file changes with StatReloader
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /accounts/profile/ HTTP/1.1" 200 12984
"GET /accounts/change-password/ HTTP/1.1" 200 11563
"GET /accounts/profile/ HTTP/1.1" 200 12984
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /accounts/profile/ HTTP/1.1" 200 12984
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /dashboard/ HTTP/1.1" 200 16103
"GET /dashboard/ HTTP/1.1" 200 16103
Watching for file changes with StatReloader
"GET / HTTP/1.1" 302 0
"GET /dashboard/ HTTP/1.1" 200 16400
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /invoices/create/ HTTP/1.1" 200 25364
"GET /products/create/ HTTP/1.1" 200 14826
"GET /products/categories/ HTTP/1.1" 200 8459
"GET /products/categories/create/ HTTP/1.1" 200 9666
Internal Server Error: /products/suppliers/
Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: products/supplier_list.html, products/supplier_list.html
"GET /products/suppliers/ HTTP/1.1" 500 93162
"GET /customers/ HTTP/1.1" 200 11216
"GET /customers/create/ HTTP/1.1" 200 15415
"GET /invoices/ HTTP/1.1" 200 11686
"GET /invoices/create/ HTTP/1.1" 200 25364
"GET /invoices/quick/ HTTP/1.1" 200 21960
"GET /customers/ HTTP/1.1" 200 11216
"GET /customers/create/ HTTP/1.1" 200 15415
"GET /customers/ HTTP/1.1" 200 11216
"GET /products/ HTTP/1.1" 200 11639
"GET /products/create/ HTTP/1.1" 200 14826
"GET /products/categories/ HTTP/1.1" 200 8459
Internal Server Error: /products/suppliers/
Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: products/supplier_list.html, products/supplier_list.html
"GET /products/suppliers/ HTTP/1.1" 500 93155
"GET /dashboard/ HTTP/1.1" 200 16400
"GET /products/create/ HTTP/1.1" 200 15013
"GET /invoices/create/ HTTP/1.1" 200 28956
Internal Server Error: /products/suppliers/
Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: products/supplier_list.html, products/supplier_list.html
"GET /products/suppliers/ HTTP/1.1" 500 93151
"GET /products/categories/ HTTP/1.1" 200 18619
"GET /dashboard/ HTTP/1.1" 200 16400
"GET /products/ HTTP/1.1" 200 66141
"GET /reports/ HTTP/1.1" 200 16906
"GET /dashboard/ HTTP/1.1" 200 16400
"GET /invoices/create/ HTTP/1.1" 200 28956
"GET /products/ HTTP/1.1" 200 66141
"GET /products/14/ HTTP/1.1" 200 15395
"GET /media/barcodes/RACKET-T_barcode.png HTTP/1.1" 200 3649
"GET /dashboard/ HTTP/1.1" 200 16400
Internal Server Error: /products/suppliers/
Traceback (most recent call last):
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "E:\Visual Studio Code\075\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: products/supplier_list.html, products/supplier_list.html
"GET /products/suppliers/ HTTP/1.1" 500 93145
"GET /products/ HTTP/1.1" 200 66141
"GET /products/create/ HTTP/1.1" 200 15013
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /products/ HTTP/1.1" 200 66141
"GET /static/css/style.css HTTP/1.1" 304 0
E:\Visual Studio Code\075\products\urls.py changed, reloading.
Watching for file changes with StatReloader
E:\Visual Studio Code\075\products\views.py changed, reloading.
Watching for file changes with StatReloader
E:\Visual Studio Code\075\products\views.py changed, reloading.
Watching for file changes with StatReloader
E:\Visual Studio Code\075\products\views.py changed, reloading.
Watching for file changes with StatReloader
E:\Visual Studio Code\075\products\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
