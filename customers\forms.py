from django import forms
from django.utils.translation import gettext_lazy as _
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Row, Column, Submit, Field
from .models import Customer


class CustomerForm(forms.ModelForm):
    """Form for creating and editing customers"""
    
    class Meta:
        model = Customer
        fields = [
            'customer_type', 'name', 'company_name', 'phone', 'email', 
            'address', 'tax_id', 'credit_limit', 'payment_terms', 'notes', 'is_active'
        ]
        widgets = {
            'customer_type': forms.Select(attrs={'class': 'form-control'}),
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'company_name': forms.TextInput(attrs={'class': 'form-control'}),
            'phone': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'tax_id': forms.TextInput(attrs={'class': 'form-control'}),
            'credit_limit': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'payment_terms': forms.NumberInput(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('customer_type', css_class='form-group col-md-6 mb-3'),
                Column('is_active', css_class='form-group col-md-6 mb-3'),
            ),
            Row(
                Column('name', css_class='form-group col-md-6 mb-3'),
                Column('company_name', css_class='form-group col-md-6 mb-3'),
            ),
            Row(
                Column('phone', css_class='form-group col-md-6 mb-3'),
                Column('email', css_class='form-group col-md-6 mb-3'),
            ),
            Field('address', css_class='mb-3'),
            Row(
                Column('tax_id', css_class='form-group col-md-4 mb-3'),
                Column('credit_limit', css_class='form-group col-md-4 mb-3'),
                Column('payment_terms', css_class='form-group col-md-4 mb-3'),
            ),
            Field('notes', css_class='mb-3'),
            Submit('submit', _('Save Customer'), css_class='btn btn-primary')
        )
    
    def clean_phone(self):
        phone = self.cleaned_data.get('phone')
        if phone and len(phone) < 10:
            raise forms.ValidationError(_('Phone number must be at least 10 digits.'))
        return phone
    
    def clean_credit_limit(self):
        credit_limit = self.cleaned_data.get('credit_limit')
        if credit_limit and credit_limit < 0:
            raise forms.ValidationError(_('Credit limit cannot be negative.'))
        return credit_limit


class CustomerSearchForm(forms.Form):
    """Form for searching and filtering customers"""
    
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Search by name, phone, or email')
        })
    )
    
    customer_type = forms.ChoiceField(
        choices=[('', _('All Types'))] + Customer.CustomerType.choices,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    is_active = forms.ChoiceField(
        choices=[
            ('', _('All Status')),
            ('true', _('Active')),
            ('false', _('Inactive')),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    has_outstanding_balance = forms.ChoiceField(
        choices=[
            ('', _('All Customers')),
            ('true', _('With Outstanding Balance')),
            ('false', _('No Outstanding Balance')),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )


class CustomerPaymentForm(forms.Form):
    """Form for recording customer payments"""
    
    amount = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        min_value=0.01,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '0.01',
            'placeholder': '0.00'
        }),
        label=_('Payment Amount')
    )
    
    payment_method = forms.ChoiceField(
        choices=[
            ('cash', _('Cash')),
            ('credit_card', _('Credit Card')),
            ('bank_transfer', _('Bank Transfer')),
            ('check', _('Check')),
        ],
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Payment Method')
    )
    
    reference_number = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Reference number (optional)')
        }),
        label=_('Reference Number')
    )
    
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': _('Payment notes (optional)')
        }),
        label=_('Notes')
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('amount', css_class='form-group col-md-6 mb-3'),
                Column('payment_method', css_class='form-group col-md-6 mb-3'),
            ),
            Field('reference_number', css_class='mb-3'),
            Field('notes', css_class='mb-3'),
            Submit('submit', _('Record Payment'), css_class='btn btn-success')
        )
