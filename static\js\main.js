// Sales Invoice Management System - Main JavaScript

$(document).ready(function() {
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
    
    // Loading overlay functions
    window.showLoading = function() {
        $('#loading-overlay').removeClass('d-none');
    };
    
    window.hideLoading = function() {
        $('#loading-overlay').addClass('d-none');
    };
    
    // AJAX setup for Django CSRF
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    
    const csrftoken = getCookie('csrftoken');
    
    $.ajaxSetup({
        beforeSend: function(xhr, settings) {
            if (!(/^http:.*/.test(settings.url) || /^https:.*/.test(settings.url))) {
                xhr.setRequestHeader("X-CSRFToken", csrftoken);
            }
        }
    });
    
    // Confirm delete actions
    $('.delete-confirm').on('click', function(e) {
        e.preventDefault();
        const url = $(this).attr('href');
        const itemName = $(this).data('item-name') || 'this item';
        
        if (confirm(`Are you sure you want to delete ${itemName}? This action cannot be undone.`)) {
            window.location.href = url;
        }
    });
    
    // Toggle user status
    $('.toggle-user-status').on('click', function(e) {
        e.preventDefault();
        const url = $(this).attr('href');
        const button = $(this);
        
        showLoading();
        
        $.post(url)
            .done(function(data) {
                if (data.success) {
                    // Update button text and class
                    if (data.is_active) {
                        button.removeClass('btn-success').addClass('btn-warning')
                               .html('<i class="fas fa-ban me-1"></i> Deactivate');
                    } else {
                        button.removeClass('btn-warning').addClass('btn-success')
                               .html('<i class="fas fa-check me-1"></i> Activate');
                    }
                    
                    // Show success message
                    showAlert('success', data.message);
                }
            })
            .fail(function() {
                showAlert('error', 'An error occurred. Please try again.');
            })
            .always(function() {
                hideLoading();
            });
    });
    
    // Show alert function
    window.showAlert = function(type, message) {
        const alertClass = type === 'error' ? 'danger' : type;
        const iconClass = type === 'success' ? 'check-circle' : 
                         type === 'error' ? 'exclamation-triangle' : 
                         type === 'warning' ? 'exclamation-circle' : 'info-circle';
        
        const alertHtml = `
            <div class="alert alert-${alertClass} alert-dismissible fade show" role="alert">
                <i class="fas fa-${iconClass} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // Remove existing alerts
        $('.alert').remove();
        
        // Add new alert
        $('.main-content').prepend(`<div class="container-fluid mt-3">${alertHtml}</div>`);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    };
    
    // Form validation
    $('form').on('submit', function() {
        const form = $(this);
        const submitBtn = form.find('button[type="submit"]');
        
        // Disable submit button to prevent double submission
        submitBtn.prop('disabled', true);
        
        // Re-enable after 3 seconds
        setTimeout(function() {
            submitBtn.prop('disabled', false);
        }, 3000);
    });
    
    // Number formatting
    $('.format-currency').each(function() {
        const value = parseFloat($(this).text());
        if (!isNaN(value)) {
            $(this).text(value.toLocaleString('en-US', {
                style: 'currency',
                currency: 'USD'
            }));
        }
    });
    
    // Search functionality
    $('#search-input').on('keyup', function() {
        const searchTerm = $(this).val().toLowerCase();
        const searchTarget = $(this).data('target') || '.searchable-item';
        
        $(searchTarget).each(function() {
            const text = $(this).text().toLowerCase();
            if (text.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });
    
    // Print functionality
    $('.print-btn').on('click', function() {
        window.print();
    });
    
    // Export functionality
    $('.export-btn').on('click', function(e) {
        e.preventDefault();
        const url = $(this).attr('href');
        const format = $(this).data('format') || 'pdf';
        
        showLoading();
        
        // Create a temporary link to download the file
        const link = document.createElement('a');
        link.href = url;
        link.download = `export.${format}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        setTimeout(hideLoading, 1000);
    });
    
    // Auto-save functionality for forms
    $('.auto-save').on('change', function() {
        const form = $(this).closest('form');
        const url = form.data('auto-save-url');
        
        if (url) {
            $.post(url, form.serialize())
                .done(function() {
                    showAlert('success', 'Changes saved automatically.');
                })
                .fail(function() {
                    showAlert('warning', 'Auto-save failed. Please save manually.');
                });
        }
    });
    
    // Sidebar toggle for mobile
    $('.sidebar-toggle').on('click', function() {
        $('.sidebar').toggleClass('show');
    });
    
    // Close sidebar when clicking outside on mobile
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.sidebar, .sidebar-toggle').length) {
            $('.sidebar').removeClass('show');
        }
    });
    
});

// Utility functions
window.formatCurrency = function(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
};

window.formatDate = function(date) {
    return new Date(date).toLocaleDateString();
};

window.formatDateTime = function(datetime) {
    return new Date(datetime).toLocaleString();
};
