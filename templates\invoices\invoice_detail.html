{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Invoice" %} {{ invoice.invoice_number }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{% trans "Invoice" %} {{ invoice.invoice_number }}</h1>
            <p class="text-muted">{{ invoice.customer.name }} - {{ invoice.invoice_date|date:"M d, Y" }}</p>
        </div>
        <div class="btn-group" role="group">
            <a href="{% url 'invoices:invoice_pdf' invoice.pk %}" class="btn btn-outline-primary">
                <i class="fas fa-file-pdf me-2"></i>{% trans "Download PDF" %}
            </a>
            {% if can_edit and invoice.status == 'draft' %}
                <a href="{% url 'invoices:invoice_update' invoice.pk %}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>{% trans "Edit Invoice" %}
                </a>
            {% endif %}
            <a href="{% url 'invoices:invoice_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Invoices" %}
            </a>
        </div>
    </div>
    
    <div class="row">
        <!-- Invoice Details -->
        <div class="col-lg-8">
            <!-- Invoice Header -->
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-primary">{{ COMPANY_NAME }}</h5>
                            <p class="mb-1">{{ COMPANY_ADDRESS }}</p>
                            <p class="mb-1">{% trans "Phone" %}: {{ COMPANY_PHONE }}</p>
                            <p class="mb-1">{% trans "Email" %}: {{ COMPANY_EMAIL }}</p>
                            <p class="mb-0">{% trans "Tax ID" %}: {{ COMPANY_TAX_ID }}</p>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <h2 class="text-primary">{% trans "INVOICE" %}</h2>
                            <p class="mb-1"><strong>{% trans "Invoice #" %}:</strong> {{ invoice.invoice_number }}</p>
                            <p class="mb-1"><strong>{% trans "Date" %}:</strong> {{ invoice.invoice_date|date:"M d, Y" }}</p>
                            <p class="mb-1"><strong>{% trans "Due Date" %}:</strong> {{ invoice.due_date|date:"M d, Y" }}</p>
                            <p class="mb-0">
                                <span class="badge bg-{% if invoice.status == 'paid' %}success{% elif invoice.status == 'pending' %}warning{% elif invoice.status == 'overdue' %}danger{% elif invoice.status == 'draft' %}secondary{% else %}info{% endif %} fs-6">
                                    {{ invoice.get_status_display }}
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Customer Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-user me-2"></i>{% trans "Bill To" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>{{ invoice.customer.name }}</h6>
                            {% if invoice.customer.company_name %}
                                <p class="mb-1">{{ invoice.customer.company_name }}</p>
                            {% endif %}
                            {% if invoice.customer.address %}
                                <p class="mb-1">{{ invoice.customer.address }}</p>
                            {% endif %}
                            <p class="mb-1">{% trans "Phone" %}: {{ invoice.customer.phone }}</p>
                            {% if invoice.customer.email %}
                                <p class="mb-0">{% trans "Email" %}: {{ invoice.customer.email }}</p>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            {% if invoice.customer.tax_id %}
                                <p class="mb-1"><strong>{% trans "Tax ID" %}:</strong> {{ invoice.customer.tax_id }}</p>
                            {% endif %}
                            <p class="mb-1"><strong>{% trans "Payment Terms" %}:</strong> {{ invoice.customer.payment_terms }} {% trans "days" %}</p>
                            <p class="mb-0"><strong>{% trans "Payment Method" %}:</strong> {{ invoice.get_payment_method_display }}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Invoice Items -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-list me-2"></i>{% trans "Invoice Items" %}
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>{% trans "Product" %}</th>
                                    <th class="text-center">{% trans "Quantity" %}</th>
                                    <th class="text-end">{% trans "Unit Price" %}</th>
                                    <th class="text-end">{% trans "Total" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in invoice.items.all %}
                                    <tr>
                                        <td>
                                            <div class="fw-bold">{{ item.product.name }}</div>
                                            <small class="text-muted">SKU: {{ item.product.sku }}</small>
                                        </td>
                                        <td class="text-center">{{ item.quantity }}</td>
                                        <td class="text-end">${{ item.unit_price }}</td>
                                        <td class="text-end fw-bold">${{ item.line_total }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <td colspan="3" class="text-end fw-bold">{% trans "Subtotal" %}:</td>
                                    <td class="text-end fw-bold">${{ invoice.subtotal }}</td>
                                </tr>
                                {% if invoice.discount_amount > 0 %}
                                <tr>
                                    <td colspan="3" class="text-end">{% trans "Discount" %} ({{ invoice.discount_percentage }}%):</td>
                                    <td class="text-end">-${{ invoice.discount_amount }}</td>
                                </tr>
                                {% endif %}
                                {% if invoice.tax_amount > 0 %}
                                <tr>
                                    <td colspan="3" class="text-end">{% trans "Tax" %} ({{ invoice.tax_rate }}%):</td>
                                    <td class="text-end">${{ invoice.tax_amount }}</td>
                                </tr>
                                {% endif %}
                                <tr class="table-primary">
                                    <td colspan="3" class="text-end fw-bold fs-5">{% trans "Total" %}:</td>
                                    <td class="text-end fw-bold fs-5">${{ invoice.total_amount }}</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Notes and Terms -->
            {% if invoice.notes or invoice.terms_and_conditions %}
            <div class="card mt-4">
                <div class="card-body">
                    {% if invoice.notes %}
                        <div class="mb-3">
                            <h6>{% trans "Notes" %}</h6>
                            <p class="text-muted">{{ invoice.notes }}</p>
                        </div>
                    {% endif %}
                    {% if invoice.terms_and_conditions %}
                        <div>
                            <h6>{% trans "Terms and Conditions" %}</h6>
                            <p class="text-muted">{{ invoice.terms_and_conditions }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Invoice Status -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>{% trans "Invoice Status" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <span class="badge bg-{% if invoice.status == 'paid' %}success{% elif invoice.status == 'pending' %}warning{% elif invoice.status == 'overdue' %}danger{% elif invoice.status == 'draft' %}secondary{% else %}info{% endif %} fs-6 px-3 py-2">
                            {{ invoice.get_status_display }}
                        </span>
                    </div>
                    
                    {% if can_edit and invoice.status != 'paid' %}
                    <div class="d-grid gap-2">
                        {% if invoice.status == 'draft' %}
                            <form method="post" action="{% url 'invoices:update_invoice_status' invoice.pk %}">
                                {% csrf_token %}
                                <input type="hidden" name="status" value="pending">
                                <button type="submit" class="btn btn-warning btn-sm">
                                    <i class="fas fa-paper-plane me-1"></i>{% trans "Send Invoice" %}
                                </button>
                            </form>
                        {% endif %}
                        
                        {% if invoice.status in 'pending,overdue' %}
                            <a href="{% url 'invoices:record_payment' invoice.pk %}" class="btn btn-success btn-sm">
                                <i class="fas fa-credit-card me-1"></i>{% trans "Record Payment" %}
                            </a>
                        {% endif %}
                        
                        {% if invoice.status == 'draft' %}
                            <form method="post" action="{% url 'invoices:update_invoice_status' invoice.pk %}">
                                {% csrf_token %}
                                <input type="hidden" name="status" value="cancelled">
                                <button type="submit" class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-times me-1"></i>{% trans "Cancel Invoice" %}
                                </button>
                            </form>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Payment History -->
            {% if payments %}
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-history me-2"></i>{% trans "Payment History" %}
                    </h6>
                </div>
                <div class="card-body">
                    {% for payment in payments %}
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <div class="fw-bold">${{ payment.amount }}</div>
                                <small class="text-muted">{{ payment.payment_date|date:"M d, Y" }}</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-{% if payment.status == 'completed' %}success{% elif payment.status == 'pending' %}warning{% else %}danger{% endif %}">
                                    {{ payment.get_status_display }}
                                </span>
                                <br>
                                <small class="text-muted">{{ payment.get_payment_method_display }}</small>
                            </div>
                        </div>
                        {% if not forloop.last %}<hr class="my-2">{% endif %}
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            
            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>{% trans "Quick Actions" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'invoices:invoice_pdf' invoice.pk %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-file-pdf me-2"></i>{% trans "Download PDF" %}
                        </a>
                        <button type="button" class="btn btn-outline-info btn-sm print-btn">
                            <i class="fas fa-print me-2"></i>{% trans "Print Invoice" %}
                        </button>
                        {% if invoice.customer.email %}
                        <a href="mailto:{{ invoice.customer.email }}?subject=Invoice {{ invoice.invoice_number }}" 
                           class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-envelope me-2"></i>{% trans "Email Customer" %}
                        </a>
                        {% endif %}
                        {% if can_edit and invoice.status == 'draft' %}
                        <a href="{% url 'invoices:invoice_update' invoice.pk %}" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-edit me-2"></i>{% trans "Edit Invoice" %}
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Invoice Summary -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>{% trans "Summary" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h5 class="text-primary">${{ invoice.total_amount }}</h5>
                            <small class="text-muted">{% trans "Total Amount" %}</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h5 class="text-info">{{ invoice.items.count }}</h5>
                            <small class="text-muted">{% trans "Items" %}</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-muted">{{ invoice.created_at|date:"M d" }}</h5>
                            <small class="text-muted">{% trans "Created" %}</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-muted">{{ invoice.due_date|date:"M d" }}</h5>
                            <small class="text-muted">{% trans "Due Date" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
