{% extends 'base.html' %}
{% load i18n crispy_forms_tags %}

{% block title %}{% trans "Quick Invoice" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{% trans "Quick Invoice" %}</h1>
            <p class="text-muted">{% trans "Fast POS-style invoice creation" %}</p>
        </div>
        <a href="{% url 'invoices:invoice_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Invoices" %}
        </a>
    </div>
    
    <div class="row">
        <!-- Product Selection -->
        <div class="col-lg-8">
            <!-- Search Bar -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control form-control-lg" id="product-search" 
                               placeholder="{% trans 'Search products by name or scan barcode...' %}" autofocus>
                    </div>
                </div>
            </div>
            
            <!-- Product Grid -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-box me-2"></i>{% trans "Products" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row" id="product-grid">
                        {% for product in products %}
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-3 product-card" 
                                 data-product-id="{{ product.id }}"
                                 data-product-name="{{ product.name }}"
                                 data-product-price="{{ product.selling_price }}"
                                 data-product-stock="{{ product.stock_quantity }}">
                                <div class="card h-100 product-item" style="cursor: pointer;">
                                    <div class="card-body text-center p-3">
                                        {% if product.product_image %}
                                            <img src="{{ product.product_image.url }}" alt="{{ product.name }}" 
                                                 class="img-fluid rounded mb-2" style="max-height: 60px;">
                                        {% else %}
                                            <i class="fas fa-box text-muted mb-2" style="font-size: 2rem;"></i>
                                        {% endif %}
                                        <h6 class="card-title mb-1">{{ product.name|truncatechars:20 }}</h6>
                                        <p class="card-text">
                                            <strong class="text-primary">${{ product.selling_price }}</strong><br>
                                            <small class="text-muted">{{ product.stock_quantity }} {% trans "in stock" %}</small>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Invoice Cart -->
        <div class="col-lg-4">
            <!-- Customer Selection -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-user me-2"></i>{% trans "Customer" %}
                    </h6>
                </div>
                <div class="card-body">
                    {% crispy form %}
                </div>
            </div>
            
            <!-- Cart Items -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>{% trans "Cart" %}
                    </h6>
                    <button type="button" class="btn btn-sm btn-outline-danger" id="clear-cart">
                        <i class="fas fa-trash me-1"></i>{% trans "Clear" %}
                    </button>
                </div>
                <div class="card-body p-0">
                    <div id="cart-items" class="list-group list-group-flush">
                        <div class="list-group-item text-center text-muted py-4" id="empty-cart">
                            <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                            <p class="mb-0">{% trans "Cart is empty" %}</p>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <!-- Cart Summary -->
                    <div class="d-flex justify-content-between mb-2">
                        <span>{% trans "Subtotal" %}:</span>
                        <span id="cart-subtotal">$0.00</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>{% trans "Tax" %} (<span id="tax-rate">0</span>%):</span>
                        <span id="cart-tax">$0.00</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between fw-bold fs-5">
                        <span>{% trans "Total" %}:</span>
                        <span id="cart-total">$0.00</span>
                    </div>
                    
                    <!-- Tax Rate Input -->
                    <div class="mt-3">
                        <label for="tax-input" class="form-label small">{% trans "Tax Rate (%)" %}</label>
                        <input type="number" class="form-control form-control-sm" id="tax-input" 
                               value="0" min="0" max="100" step="0.01">
                    </div>
                    
                    <!-- Checkout Button -->
                    <div class="d-grid mt-3">
                        <button type="button" class="btn btn-success btn-lg" id="checkout-btn" disabled>
                            <i class="fas fa-check me-2"></i>{% trans "Create Invoice" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div class="modal fade" id="successModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>{% trans "Invoice Created" %}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <h4 id="invoice-number"></h4>
                <p class="text-muted">{% trans "Invoice has been created successfully" %}</p>
                <h5 class="text-success" id="invoice-total"></h5>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                <a href="#" class="btn btn-primary" id="view-invoice-btn">{% trans "View Invoice" %}</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    let cart = [];
    
    // Add product to cart
    function addToCart(productId, productName, productPrice, productStock) {
        const existingItem = cart.find(item => item.id == productId);
        
        if (existingItem) {
            if (existingItem.quantity < productStock) {
                existingItem.quantity++;
                updateCartDisplay();
            } else {
                showAlert('warning', 'Insufficient stock available');
            }
        } else {
            cart.push({
                id: productId,
                name: productName,
                price: parseFloat(productPrice),
                quantity: 1,
                stock: productStock
            });
            updateCartDisplay();
        }
    }
    
    // Update cart display
    function updateCartDisplay() {
        const cartItems = $('#cart-items');
        const emptyCart = $('#empty-cart');
        
        if (cart.length === 0) {
            cartItems.html('<div class="list-group-item text-center text-muted py-4" id="empty-cart"><i class="fas fa-shopping-cart fa-2x mb-2"></i><p class="mb-0">Cart is empty</p></div>');
            $('#checkout-btn').prop('disabled', true);
        } else {
            let html = '';
            let subtotal = 0;
            
            cart.forEach((item, index) => {
                const lineTotal = item.price * item.quantity;
                subtotal += lineTotal;
                
                html += `
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">${item.name}</h6>
                                <small class="text-muted">$${item.price.toFixed(2)} each</small>
                            </div>
                            <div class="d-flex align-items-center">
                                <button class="btn btn-sm btn-outline-secondary quantity-btn" data-action="decrease" data-index="${index}">-</button>
                                <span class="mx-2 fw-bold">${item.quantity}</span>
                                <button class="btn btn-sm btn-outline-secondary quantity-btn" data-action="increase" data-index="${index}">+</button>
                                <button class="btn btn-sm btn-outline-danger ms-2 remove-item" data-index="${index}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="text-end mt-2">
                            <strong>$${lineTotal.toFixed(2)}</strong>
                        </div>
                    </div>
                `;
            });
            
            cartItems.html(html);
            
            // Calculate totals
            const taxRate = parseFloat($('#tax-input').val()) || 0;
            const taxAmount = (subtotal * taxRate) / 100;
            const total = subtotal + taxAmount;
            
            $('#cart-subtotal').text('$' + subtotal.toFixed(2));
            $('#cart-tax').text('$' + taxAmount.toFixed(2));
            $('#cart-total').text('$' + total.toFixed(2));
            $('#tax-rate').text(taxRate);
            
            $('#checkout-btn').prop('disabled', false);
        }
    }
    
    // Product click handler
    $(document).on('click', '.product-item', function() {
        const card = $(this).closest('.product-card');
        const productId = card.data('product-id');
        const productName = card.data('product-name');
        const productPrice = card.data('product-price');
        const productStock = card.data('product-stock');
        
        if (productStock > 0) {
            addToCart(productId, productName, productPrice, productStock);
        } else {
            showAlert('error', 'Product is out of stock');
        }
    });
    
    // Quantity change handlers
    $(document).on('click', '.quantity-btn', function() {
        const action = $(this).data('action');
        const index = $(this).data('index');
        const item = cart[index];
        
        if (action === 'increase' && item.quantity < item.stock) {
            item.quantity++;
        } else if (action === 'decrease' && item.quantity > 1) {
            item.quantity--;
        }
        
        updateCartDisplay();
    });
    
    // Remove item handler
    $(document).on('click', '.remove-item', function() {
        const index = $(this).data('index');
        cart.splice(index, 1);
        updateCartDisplay();
    });
    
    // Clear cart
    $('#clear-cart').click(function() {
        cart = [];
        updateCartDisplay();
    });
    
    // Tax rate change
    $('#tax-input').on('input', updateCartDisplay);
    
    // Product search
    $('#product-search').on('input', function() {
        const query = $(this).val().toLowerCase();
        
        $('.product-card').each(function() {
            const productName = $(this).data('product-name').toLowerCase();
            if (productName.includes(query) || query === '') {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });
    
    // Checkout
    $('#checkout-btn').click(function() {
        const customerId = $('#id_customer').val();
        const paymentMethod = $('#id_payment_method').val();
        const taxRate = parseFloat($('#tax-input').val()) || 0;
        
        if (!customerId) {
            showAlert('error', 'Please select a customer');
            return;
        }
        
        if (cart.length === 0) {
            showAlert('error', 'Cart is empty');
            return;
        }
        
        const data = {
            customer_id: customerId,
            payment_method: paymentMethod,
            tax_rate: taxRate,
            items: cart.map(item => ({
                product_id: item.id,
                quantity: item.quantity,
                unit_price: item.price
            }))
        };
        
        showLoading();
        
        $.ajax({
            url: '{% url "invoices:quick_invoice" %}',
            method: 'POST',
            data: JSON.stringify(data),
            contentType: 'application/json',
            headers: {
                'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(response) {
                hideLoading();
                if (response.success) {
                    $('#invoice-number').text('Invoice #' + response.invoice_number);
                    $('#invoice-total').text('$' + response.total_amount);
                    $('#view-invoice-btn').attr('href', '/invoices/' + response.invoice_id + '/');
                    $('#successModal').modal('show');
                    
                    // Clear cart
                    cart = [];
                    updateCartDisplay();
                    $('#id_customer').val('');
                    $('#id_payment_method').val('cash');
                    $('#tax-input').val(0);
                } else {
                    showAlert('error', response.error);
                }
            },
            error: function() {
                hideLoading();
                showAlert('error', 'An error occurred. Please try again.');
            }
        });
    });
    
    // Barcode scanning (Enter key)
    $('#product-search').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            const query = $(this).val();
            const product = $('.product-card').filter(function() {
                return $(this).data('product-name').toLowerCase() === query.toLowerCase();
            }).first();
            
            if (product.length) {
                product.find('.product-item').click();
                $(this).val('');
            }
        }
    });
});
</script>
{% endblock %}
