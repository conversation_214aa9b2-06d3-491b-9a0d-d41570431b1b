from django import template
from django.utils import translation
from dashboard.translations import translate

register = template.Library()

@register.simple_tag
def trans(text):
    """
    Simple translation tag for Arabic
    """
    current_language = translation.get_language()
    if current_language == 'ar':
        return translate(text, 'ar')
    return text

@register.filter
def arabic_trans(text):
    """
    Filter for translating text to Arabic
    """
    current_language = translation.get_language()
    if current_language == 'ar':
        return translate(text, 'ar')
    return text
