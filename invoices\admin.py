from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import Invoice, InvoiceItem, Payment, PurchaseOrder


class InvoiceItemInline(admin.TabularInline):
    """Inline for Invoice Items"""
    model = InvoiceItem
    extra = 1
    readonly_fields = ['line_total']


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    """Invoice Admin"""
    list_display = ['invoice_number', 'customer', 'invoice_date', 'total_amount', 'status', 'created_by']
    list_filter = ['status', 'payment_method', 'invoice_date', 'created_at']
    search_fields = ['invoice_number', 'customer__name']
    ordering = ['-created_at']
    readonly_fields = ['invoice_number', 'uuid', 'subtotal', 'tax_amount', 'total_amount', 'created_at', 'updated_at']
    inlines = [InvoiceItemInline]

    fieldsets = (
        (_('Invoice Information'), {
            'fields': ('invoice_number', 'uuid', 'customer', 'created_by')
        }),
        (_('Dates'), {
            'fields': ('invoice_date', 'due_date')
        }),
        (_('Financial Details'), {
            'fields': ('subtotal', 'tax_rate', 'tax_amount', 'discount_percentage', 'discount_amount', 'total_amount')
        }),
        (_('Payment'), {
            'fields': ('status', 'payment_method')
        }),
        (_('Additional Information'), {
            'fields': ('notes', 'terms_and_conditions')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    """Payment Admin"""
    list_display = ['invoice', 'amount', 'payment_method', 'payment_date', 'status', 'created_by']
    list_filter = ['payment_method', 'status', 'payment_date']
    search_fields = ['invoice__invoice_number', 'reference_number']
    ordering = ['-payment_date']


@admin.register(PurchaseOrder)
class PurchaseOrderAdmin(admin.ModelAdmin):
    """Purchase Order Admin"""
    list_display = ['po_number', 'supplier', 'order_date', 'total_amount', 'status', 'created_by']
    list_filter = ['status', 'order_date', 'created_at']
    search_fields = ['po_number', 'supplier__name']
    ordering = ['-created_at']
