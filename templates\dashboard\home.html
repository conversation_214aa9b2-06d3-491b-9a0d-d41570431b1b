{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Dashboard" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="display-4 mb-2">
                                {% trans "Welcome back" %}, {{ user.first_name|default:user.username }}!
                            </h1>
                            <p class="lead mb-0">
                                {% trans "Here's what's happening with your business today." %}
                            </p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <i class="fas fa-chart-line" style="font-size: 4rem; opacity: 0.3;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card primary">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">{% trans "Today's Sales" %}</h6>
                            <h3 class="mb-0">$0.00</h3>
                            <small class="text-success">
                                <i class="fas fa-arrow-up me-1"></i>0% {% trans "from yesterday" %}
                            </small>
                        </div>
                        <div class="text-primary">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card success">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">{% trans "Total Invoices" %}</h6>
                            <h3 class="mb-0">0</h3>
                            <small class="text-muted">
                                {% trans "This month" %}
                            </small>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-file-invoice fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">{% trans "Total Customers" %}</h6>
                            <h3 class="mb-0">0</h3>
                            <small class="text-muted">
                                {% trans "Active customers" %}
                            </small>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card info">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">{% trans "Low Stock Items" %}</h6>
                            <h3 class="mb-0">0</h3>
                            <small class="text-danger">
                                {% trans "Need attention" %}
                            </small>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>{% trans "Quick Actions" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if user.role == 'admin' or user.role == 'sales_employee' %}
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="#" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                <span>{% trans "Create Invoice" %}</span>
                            </a>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="#" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                <i class="fas fa-user-plus fa-2x mb-2"></i>
                                <span>{% trans "Add Customer" %}</span>
                            </a>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="#" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                <i class="fas fa-box fa-2x mb-2"></i>
                                <span>{% trans "Add Product" %}</span>
                            </a>
                        </div>
                        {% endif %}
                        
                        {% if user.role == 'admin' or user.role == 'accountant' %}
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="#" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                <span>{% trans "View Reports" %}</span>
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-clock me-2"></i>{% trans "Recent Invoices" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center py-5">
                        <i class="fas fa-file-invoice text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3 text-muted">{% trans "No recent invoices" %}</h5>
                        <p class="text-muted">{% trans "Create your first invoice to see it here." %}</p>
                        {% if user.role == 'admin' or user.role == 'sales_employee' %}
                        <a href="#" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>{% trans "Create Invoice" %}
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bell me-2"></i>{% trans "Notifications" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center py-4">
                        <i class="fas fa-bell-slash text-muted" style="font-size: 2rem;"></i>
                        <p class="mt-3 text-muted mb-0">{% trans "No new notifications" %}</p>
                    </div>
                </div>
            </div>
            
            <!-- System Info -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>{% trans "System Info" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">{% trans "Version" %}</span>
                        <span class="fw-bold">1.0.0</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">{% trans "Your Role" %}</span>
                        <span class="badge bg-{% if user.role == 'admin' %}danger{% elif user.role == 'accountant' %}warning{% else %}info{% endif %}">
                            {{ user.get_role_display }}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span class="text-muted">{% trans "Last Login" %}</span>
                        <span class="fw-bold">{{ user.last_login|date:"M d, H:i" }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .dashboard-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .stat-card {
        transition: transform 0.2s ease-in-out;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
    }
</style>
{% endblock %}
