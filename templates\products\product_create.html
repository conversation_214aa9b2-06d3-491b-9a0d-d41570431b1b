{% extends 'base.html' %}
{% load i18n crispy_forms_tags %}

{% block title %}{% trans "Add Product" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- <PERSON> Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">{% trans "Add New Product" %}</h1>
                    <p class="text-muted">{% trans "Add a new product to your inventory" %}</p>
                </div>
                <a href="{% url 'products:product_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Products" %}
                </a>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-plus me-2"></i>{% trans "Product Information" %}
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        {% crispy form %}
                    </form>
                </div>
            </div>
            
            <!-- Help Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>{% trans "Product Guidelines" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">{% trans "SKU Guidelines" %}</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-check text-success me-1"></i> {% trans "Use unique product codes" %}</li>
                                <li><i class="fas fa-check text-success me-1"></i> {% trans "Keep it short and memorable" %}</li>
                                <li><i class="fas fa-check text-success me-1"></i> {% trans "Use letters and numbers only" %}</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">{% trans "Pricing Tips" %}</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-check text-success me-1"></i> {% trans "Set competitive selling prices" %}</li>
                                <li><i class="fas fa-check text-success me-1"></i> {% trans "Include all costs in cost price" %}</li>
                                <li><i class="fas fa-check text-success me-1"></i> {% trans "Consider profit margins" %}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-generate SKU from product name
    $('#id_name').on('input', function() {
        const name = $(this).val();
        const sku = name.toUpperCase()
                       .replace(/[^A-Z0-9]/g, '')
                       .substring(0, 10);
        if (!$('#id_sku').val()) {
            $('#id_sku').val(sku);
        }
    });
    
    // Calculate profit margin
    function calculateMargin() {
        const costPrice = parseFloat($('#id_cost_price').val()) || 0;
        const sellingPrice = parseFloat($('#id_selling_price').val()) || 0;
        
        if (costPrice > 0) {
            const margin = ((sellingPrice - costPrice) / costPrice * 100).toFixed(2);
            $('#profit-margin').text(margin + '%');
        }
    }
    
    $('#id_cost_price, #id_selling_price').on('input', calculateMargin);
});
</script>
{% endblock %}
