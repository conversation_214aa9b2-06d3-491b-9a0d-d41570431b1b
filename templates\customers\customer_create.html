{% extends 'base.html' %}
{% load i18n crispy_forms_tags %}

{% block title %}{% trans "Add Customer" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">{% trans "Add New Customer" %}</h1>
                    <p class="text-muted">{% trans "Add a new customer to your CRM system" %}</p>
                </div>
                <a href="{% url 'customers:customer_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Customers" %}
                </a>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>{% trans "Customer Information" %}
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        {% crispy form %}
                    </form>
                </div>
            </div>
            
            <!-- Customer Guidelines -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>{% trans "Customer Guidelines" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">{% trans "Customer Types" %}</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-user text-info me-1"></i> <strong>{% trans "Individual" %}:</strong> {% trans "Personal customers" %}</li>
                                <li><i class="fas fa-building text-info me-1"></i> <strong>{% trans "Business" %}:</strong> {% trans "Company customers" %}</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">{% trans "Credit Management" %}</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-check text-success me-1"></i> {% trans "Set appropriate credit limits" %}</li>
                                <li><i class="fas fa-check text-success me-1"></i> {% trans "Define payment terms clearly" %}</li>
                                <li><i class="fas fa-check text-success me-1"></i> {% trans "Monitor outstanding balances" %}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Show/hide company name field based on customer type
    function toggleCompanyField() {
        const customerType = $('#id_customer_type').val();
        const companyField = $('#id_company_name').closest('.form-group');
        
        if (customerType === 'business') {
            companyField.show();
            $('#id_company_name').attr('required', true);
        } else {
            companyField.hide();
            $('#id_company_name').attr('required', false);
        }
    }
    
    $('#id_customer_type').on('change', toggleCompanyField);
    toggleCompanyField(); // Initial call
    
    // Format phone number
    $('#id_phone').on('input', function() {
        let value = $(this).val().replace(/\D/g, '');
        if (value.length >= 10) {
            value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
        }
        $(this).val(value);
    });
    
    // Calculate available credit
    function calculateAvailableCredit() {
        const creditLimit = parseFloat($('#id_credit_limit').val()) || 0;
        $('#available-credit').text('$' + creditLimit.toFixed(2));
    }
    
    $('#id_credit_limit').on('input', calculateAvailableCredit);
    calculateAvailableCredit(); // Initial call
});
</script>
{% endblock %}
