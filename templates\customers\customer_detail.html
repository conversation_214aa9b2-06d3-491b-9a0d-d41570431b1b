{% extends 'base.html' %}
{% load i18n %}

{% block title %}{{ customer.name }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ customer.name }}</h1>
            <p class="text-muted">
                {% if customer.company_name %}{{ customer.company_name }} - {% endif %}
                {% trans "Customer Details" %}
            </p>
        </div>
        <div class="btn-group" role="group">
            <a href="{% url 'customers:customer_update' customer.pk %}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>{% trans "Edit Customer" %}
            </a>
            <a href="{% url 'customers:customer_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Customers" %}
            </a>
        </div>
    </div>
    
    <div class="row">
        <!-- Customer Information -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-user me-2"></i>{% trans "Customer Information" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">{% trans "Name" %}:</td>
                                    <td>{{ customer.name }}</td>
                                </tr>
                                {% if customer.company_name %}
                                <tr>
                                    <td class="fw-bold">{% trans "Company" %}:</td>
                                    <td>{{ customer.company_name }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td class="fw-bold">{% trans "Type" %}:</td>
                                    <td>
                                        <span class="badge bg-{% if customer.customer_type == 'business' %}info{% else %}secondary{% endif %}">
                                            {{ customer.get_customer_type_display }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{% trans "Phone" %}:</td>
                                    <td>{{ customer.phone }}</td>
                                </tr>
                                {% if customer.email %}
                                <tr>
                                    <td class="fw-bold">{% trans "Email" %}:</td>
                                    <td>{{ customer.email }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                {% if customer.tax_id %}
                                <tr>
                                    <td class="fw-bold">{% trans "Tax ID" %}:</td>
                                    <td>{{ customer.tax_id }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td class="fw-bold">{% trans "Credit Limit" %}:</td>
                                    <td class="text-primary fw-bold">${{ customer.credit_limit }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{% trans "Payment Terms" %}:</td>
                                    <td>{{ customer.payment_terms }} {% trans "days" %}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{% trans "Status" %}:</td>
                                    <td>
                                        <span class="badge bg-{% if customer.is_active %}success{% else %}danger{% endif %}">
                                            {% if customer.is_active %}{% trans "Active" %}{% else %}{% trans "Inactive" %}{% endif %}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{% trans "Created" %}:</td>
                                    <td>{{ customer.created_at|date:"M d, Y" }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    {% if customer.address %}
                        <div class="mt-3">
                            <h6>{% trans "Address" %}</h6>
                            <p class="text-muted">{{ customer.address }}</p>
                        </div>
                    {% endif %}
                    
                    {% if customer.notes %}
                        <div class="mt-3">
                            <h6>{% trans "Notes" %}</h6>
                            <p class="text-muted">{{ customer.notes }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Recent Invoices -->
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-file-invoice me-2"></i>{% trans "Recent Invoices" %}
                    </h6>
                    <a href="{% url 'customers:customer_purchase_history' customer.pk %}" class="btn btn-sm btn-outline-primary">
                        {% trans "View All" %}
                    </a>
                </div>
                <div class="card-body">
                    {% if invoices %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{% trans "Invoice #" %}</th>
                                        <th>{% trans "Date" %}</th>
                                        <th>{% trans "Amount" %}</th>
                                        <th>{% trans "Status" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for invoice in invoices %}
                                        <tr>
                                            <td><code>{{ invoice.invoice_number }}</code></td>
                                            <td>{{ invoice.invoice_date|date:"M d, Y" }}</td>
                                            <td>${{ invoice.total_amount }}</td>
                                            <td>
                                                <span class="badge bg-{% if invoice.status == 'paid' %}success{% elif invoice.status == 'pending' %}warning{% elif invoice.status == 'overdue' %}danger{% else %}secondary{% endif %}">
                                                    {{ invoice.get_status_display }}
                                                </span>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-file-invoice text-muted" style="font-size: 2rem;"></i>
                            <p class="mt-2 text-muted mb-0">{% trans "No invoices yet" %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Statistics and Quick Actions -->
        <div class="col-lg-4">
            <!-- Customer Statistics -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>{% trans "Customer Statistics" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h4 class="text-primary">${{ stats.total_purchases }}</h4>
                            <small class="text-muted">{% trans "Total Purchases" %}</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-info">{{ stats.total_invoices }}</h4>
                            <small class="text-muted">{% trans "Total Invoices" %}</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-{% if stats.outstanding_balance > 0 %}warning{% else %}success{% endif %}">
                                ${{ stats.outstanding_balance }}
                            </h4>
                            <small class="text-muted">{% trans "Outstanding" %}</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">${{ stats.available_credit }}</h4>
                            <small class="text-muted">{% trans "Available Credit" %}</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>{% trans "Quick Actions" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="#" class="btn btn-outline-primary">
                            <i class="fas fa-plus me-2"></i>{% trans "Create Invoice" %}
                        </a>
                        <a href="{% url 'customers:customer_purchase_history' customer.pk %}" class="btn btn-outline-info">
                            <i class="fas fa-history me-2"></i>{% trans "Purchase History" %}
                        </a>
                        <a href="{% url 'customers:customer_update' customer.pk %}" class="btn btn-outline-warning">
                            <i class="fas fa-edit me-2"></i>{% trans "Edit Customer" %}
                        </a>
                        {% if customer.phone %}
                        <a href="tel:{{ customer.phone }}" class="btn btn-outline-success">
                            <i class="fas fa-phone me-2"></i>{% trans "Call Customer" %}
                        </a>
                        {% endif %}
                        {% if customer.email %}
                        <a href="mailto:{{ customer.email }}" class="btn btn-outline-secondary">
                            <i class="fas fa-envelope me-2"></i>{% trans "Send Email" %}
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Credit Status -->
            {% if customer.credit_limit > 0 %}
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>{% trans "Credit Status" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="small">{% trans "Credit Used" %}</span>
                            <span class="small">${{ stats.outstanding_balance }} / ${{ customer.credit_limit }}</span>
                        </div>
                        <div class="progress">
                            {% with credit_percentage=stats.outstanding_balance|floatformat:2|add:0|div:customer.credit_limit|mul:100 %}
                                <div class="progress-bar bg-{% if credit_percentage > 80 %}danger{% elif credit_percentage > 60 %}warning{% else %}success{% endif %}" 
                                     style="width: {{ credit_percentage }}%"></div>
                            {% endwith %}
                        </div>
                    </div>
                    
                    {% if stats.outstanding_balance > customer.credit_limit %}
                        <div class="alert alert-danger alert-sm">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            {% trans "Credit limit exceeded!" %}
                        </div>
                    {% elif stats.outstanding_balance > customer.credit_limit|mul:0.8 %}
                        <div class="alert alert-warning alert-sm">
                            <i class="fas fa-exclamation-circle me-1"></i>
                            {% trans "Approaching credit limit" %}
                        </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
