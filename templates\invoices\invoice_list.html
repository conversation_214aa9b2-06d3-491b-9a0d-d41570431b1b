{% extends 'base.html' %}
{% load i18n %}
{% load arabic_trans %}

{% block title %}{% trans "Invoices" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{% trans "Invoice Management" %}</h1>
            <p class="text-muted">{% trans "Create and manage sales invoices" %}</p>
        </div>
        <div class="btn-group" role="group">
            <a href="{% url 'invoices:invoice_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>{% trans "Create Invoice" %}
            </a>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-cog me-2"></i>{% trans "More" %}
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{% url 'invoices:quick_invoice' %}">
                        <i class="fas fa-bolt me-2"></i>{% trans "Quick Invoice" %}
                    </a></li>
                    {% if user.role == 'admin' or user.role == 'accountant' %}
                    <li><a class="dropdown-item" href="{% url 'invoices:overdue_invoices' %}">
                        <i class="fas fa-exclamation-triangle me-2"></i>{% trans "Overdue Invoices" %}
                    </a></li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    {{ form.search.label_tag }}
                    {{ form.search }}
                </div>
                <div class="col-md-2">
                    {{ form.customer.label_tag }}
                    {{ form.customer }}
                </div>
                <div class="col-md-2">
                    {{ form.status.label_tag }}
                    {{ form.status }}
                </div>
                <div class="col-md-2">
                    {{ form.payment_method.label_tag }}
                    {{ form.payment_method }}
                </div>
                <div class="col-md-1">
                    {{ form.date_from.label_tag }}
                    {{ form.date_from }}
                </div>
                <div class="col-md-1">
                    {{ form.date_to.label_tag }}
                    {{ form.date_to }}
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i>
                    </button>
                    <a href="{% url 'invoices:invoice_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Invoices Table -->
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-file-invoice me-2"></i>{% trans "Invoices" %}
                {% if invoices %}
                    <span class="badge bg-primary ms-2">{{ page_obj.paginator.count }}</span>
                {% endif %}
            </h6>
        </div>
        <div class="card-body p-0">
            {% if invoices %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>{% trans "Invoice #" %}</th>
                                <th>{% trans "Customer" %}</th>
                                <th>{% trans "Date" %}</th>
                                <th>{% trans "Amount" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Payment" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in invoices %}
                                <tr>
                                    <td>
                                        <div>
                                            <code class="fw-bold">{{ invoice.invoice_number }}</code>
                                        </div>
                                        <small class="text-muted">{{ invoice.created_at|date:"M d, Y" }}</small>
                                    </td>
                                    <td>
                                        <div class="fw-bold">{{ invoice.customer.name }}</div>
                                        {% if invoice.customer.company_name %}
                                            <small class="text-muted">{{ invoice.customer.company_name }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div>{{ invoice.invoice_date|date:"M d, Y" }}</div>
                                        <small class="text-muted">{% trans "Due" %}: {{ invoice.due_date|date:"M d" }}</small>
                                    </td>
                                    <td>
                                        <div class="fw-bold">${{ invoice.total_amount }}</div>
                                        {% if invoice.items.count %}
                                            <small class="text-muted">{{ invoice.items.count }} {% trans "items" %}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if invoice.status == 'paid' %}success{% elif invoice.status == 'pending' %}warning{% elif invoice.status == 'overdue' %}danger{% elif invoice.status == 'draft' %}secondary{% else %}info{% endif %}">
                                            {{ invoice.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if invoice.payment_method == 'cash' %}success{% elif invoice.payment_method == 'credit_card' %}info{% elif invoice.payment_method == 'bank_transfer' %}primary{% else %}secondary{% endif %}">
                                            {{ invoice.get_payment_method_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{% url 'invoices:invoice_detail' invoice.pk %}"
                                               class="btn btn-outline-info" title="{% trans 'View Details' %}">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if user.role == 'admin' or user.role == 'sales_employee' %}
                                                {% if invoice.status == 'draft' %}
                                                    <a href="{% url 'invoices:invoice_update' invoice.pk %}"
                                                       class="btn btn-outline-primary" title="{% trans 'Edit Invoice' %}">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                {% endif %}
                                            {% endif %}
                                            <a href="{% url 'invoices:invoice_pdf' invoice.pk %}"
                                               class="btn btn-outline-secondary" title="{% trans 'Download PDF' %}">
                                                <i class="fas fa-file-pdf"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                    <div class="card-footer">
                        <nav aria-label="{% trans 'Invoices pagination' %}">
                            <ul class="pagination justify-content-center mb-0">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                            {% trans "First" %}
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                            {% trans "Previous" %}
                                        </a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">
                                        {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                            {% trans "Next" %}
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                            {% trans "Last" %}
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-file-invoice text-muted" style="font-size: 3rem;"></i>
                    <h5 class="mt-3 text-muted">{% trans "No invoices found" %}</h5>
                    <p class="text-muted">{% trans "Create your first invoice to get started." %}</p>
                    <a href="{% url 'invoices:invoice_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "Create First Invoice" %}
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
