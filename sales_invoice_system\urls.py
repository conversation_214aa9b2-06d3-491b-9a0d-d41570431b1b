"""
URL configuration for sales_invoice_system project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.shortcuts import redirect
from django.conf.urls.i18n import i18n_patterns
from dashboard.language_views import set_language

urlpatterns = [
    # Language switching
    path('i18n/', include('django.conf.urls.i18n')),
    path('set_language/', set_language, name='set_language'),

    # Admin
    path('admin/', admin.site.urls),
]

# Internationalized URLs
urlpatterns += i18n_patterns(
    # Authentication
    path('accounts/', include('accounts.urls')),

    # Dashboard
    path('dashboard/', include('dashboard.urls')),

    # Products
    path('products/', include('products.urls')),

    # Customers
    path('customers/', include('customers.urls')),

    # Invoices
    path('invoices/', include('invoices.urls')),

    # Reports
    path('reports/', include('reports.urls')),

    # Root redirect to dashboard
    path('', lambda request: redirect('dashboard:home')),

    prefix_default_language=False,
)

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
