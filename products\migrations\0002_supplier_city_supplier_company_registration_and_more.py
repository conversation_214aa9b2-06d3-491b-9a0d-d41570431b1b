# Generated by Django 4.2.7 on 2025-06-17 11:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("products", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="supplier",
            name="city",
            field=models.CharField(
                blank=True, max_length=100, null=True, verbose_name="City"
            ),
        ),
        migrations.AddField(
            model_name="supplier",
            name="company_registration",
            field=models.CharField(
                blank=True,
                max_length=100,
                null=True,
                verbose_name="Company Registration",
            ),
        ),
        migrations.AddField(
            model_name="supplier",
            name="country",
            field=models.CharField(
                choices=[
                    ("QA", "Qatar"),
                    ("SA", "Saudi Arabia"),
                    ("AE", "UAE"),
                    ("KW", "Kuwait"),
                    ("BH", "Bahrain"),
                    ("OM", "Oman"),
                    ("US", "United States"),
                    ("GB", "United Kingdom"),
                    ("DE", "Germany"),
                    ("FR", "France"),
                    ("IT", "Italy"),
                    ("ES", "Spain"),
                    ("CN", "China"),
                    ("IN", "India"),
                    ("JP", "Japan"),
                    ("KR", "South Korea"),
                ],
                default="QA",
                max_length=2,
                verbose_name="Country",
            ),
        ),
        migrations.AddField(
            model_name="supplier",
            name="notes",
            field=models.TextField(blank=True, null=True, verbose_name="Notes"),
        ),
        migrations.AddField(
            model_name="supplier",
            name="website",
            field=models.URLField(blank=True, null=True, verbose_name="Website"),
        ),
    ]
