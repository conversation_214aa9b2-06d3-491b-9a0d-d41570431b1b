from django.db import models
from django.core.validators import MinValueValidator
from django.utils.translation import gettext_lazy as _
import barcode
from barcode.writer import ImageWriter
from io import BytesIO
from django.core.files import File


class Category(models.Model):
    """Product Category Model"""

    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_('Category Name')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Active Status')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )

    class Meta:
        verbose_name = _('Category')
        verbose_name_plural = _('Categories')
        ordering = ['name']

    def __str__(self):
        return self.name


class Supplier(models.Model):
    """Supplier Model for Purchase Orders"""

    name = models.CharField(
        max_length=200,
        verbose_name=_('Supplier Name')
    )

    contact_person = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Contact Person')
    )

    phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('Phone Number')
    )

    email = models.EmailField(
        blank=True,
        null=True,
        verbose_name=_('Email Address')
    )

    address = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Address')
    )

    tax_id = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Tax ID')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Active Status')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )

    class Meta:
        verbose_name = _('Supplier')
        verbose_name_plural = _('Suppliers')
        ordering = ['name']

    def __str__(self):
        return self.name


class Product(models.Model):
    """Product Model with inventory management"""

    name = models.CharField(
        max_length=200,
        verbose_name=_('Product Name')
    )

    sku = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('SKU/Product Code')
    )

    category = models.ForeignKey(
        Category,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Category')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )

    cost_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Cost Price')
    )

    selling_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name=_('Selling Price')
    )

    stock_quantity = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Stock Quantity')
    )

    min_stock_level = models.PositiveIntegerField(
        default=10,
        verbose_name=_('Minimum Stock Level')
    )

    product_image = models.ImageField(
        upload_to='products/',
        blank=True,
        null=True,
        verbose_name=_('Product Image')
    )

    barcode_image = models.ImageField(
        upload_to='barcodes/',
        blank=True,
        null=True,
        verbose_name=_('Barcode Image')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Active Status')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )

    class Meta:
        verbose_name = _('Product')
        verbose_name_plural = _('Products')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.sku})"

    @property
    def profit_margin(self):
        """Calculate profit margin percentage"""
        if self.cost_price > 0:
            return ((self.selling_price - self.cost_price) / self.cost_price) * 100
        return 0

    @property
    def is_low_stock(self):
        """Check if product is below minimum stock level"""
        return self.stock_quantity <= self.min_stock_level

    def generate_barcode(self):
        """Generate barcode image for the product"""
        if not self.sku:
            return

        # Generate barcode
        code128 = barcode.get_barcode_class('code128')
        barcode_instance = code128(self.sku, writer=ImageWriter())

        # Save to BytesIO
        buffer = BytesIO()
        barcode_instance.write(buffer)

        # Save to model field
        filename = f"{self.sku}_barcode.png"
        self.barcode_image.save(
            filename,
            File(buffer),
            save=False
        )

    def save(self, *args, **kwargs):
        # Generate barcode if SKU exists and no barcode image
        if self.sku and not self.barcode_image:
            self.generate_barcode()
        super().save(*args, **kwargs)
