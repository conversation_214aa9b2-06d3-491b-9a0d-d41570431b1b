#!/usr/bin/env python3
"""
Create a simple .mo file for Arabic translations
"""
import struct
import os

def create_mo_file():
    # Simple translations dictionary
    translations = {
        "Dashboard": "لوحة التحكم",
        "Products": "المنتجات", 
        "Customers": "العملاء",
        "Invoices": "الفواتير",
        "Reports": "التقارير",
        "Settings": "الإعدادات",
        "Logout": "تسجيل الخروج",
        "Suppliers": "الموردون",
        "Supplier Management": "إدارة الموردين",
        "Add Supplier": "إضافة مورد",
        "Edit Supplier": "تعديل المورد",
        "Delete Supplier": "حذف المورد",
        "Supplier Details": "تفاصيل المورد",
        "Supplier Name": "اسم المورد",
        "Contact Person": "الشخص المسؤول",
        "Phone": "الهاتف",
        "Email": "البريد الإلكتروني",
        "Website": "الموقع الإلكتروني",
        "Address": "العنوان",
        "City": "المدينة",
        "Country": "الدولة",
        "Company Registration": "رقم السجل التجاري",
        "Tax ID": "الرقم الضريبي",
        "Notes": "ملاحظات",
        "Active": "نشط",
        "Inactive": "غير نشط",
        "Status": "الحالة",
        "Qatar": "قطر",
        "Saudi Arabia": "المملكة العربية السعودية",
        "UAE": "الإمارات العربية المتحدة",
        "Kuwait": "الكويت",
        "Bahrain": "البحرين",
        "Oman": "عُمان",
        "Save": "حفظ",
        "Cancel": "إلغاء",
        "Delete": "حذف",
        "Edit": "تعديل",
        "View": "عرض",
        "Search": "بحث",
        "Create": "إنشاء",
        "Update": "تحديث",
        "Back": "رجوع",
        "Save Supplier": "حفظ المورد",
        "Supplier created successfully.": "تم إنشاء المورد بنجاح.",
        "Supplier updated successfully.": "تم تحديث المورد بنجاح.",
        "Supplier deleted successfully.": "تم حذف المورد بنجاح.",
        "All Products": "جميع المنتجات",
        "Add Product": "إضافة منتج",
        "Categories": "الفئات",
        "All Customers": "جميع العملاء",
        "Add Customer": "إضافة عميل",
        "All Invoices": "جميع الفواتير",
        "Create Invoice": "إنشاء فاتورة",
        "Profile": "الملف الشخصي",
        "Change Password": "تغيير كلمة المرور",
        "Name": "الاسم",
        "Description": "الوصف",
        "Yes": "نعم",
        "No": "لا",
    }
    
    # Create MO file content
    keys = list(translations.keys())
    values = list(translations.values())
    
    # MO file format
    magic = 0x950412de
    version = 0
    msgcount = len(translations)
    masteridx = 7 * 4 + 16 * msgcount
    transidx = masteridx + 8 * msgcount
    
    # Calculate offsets
    koffsets = []
    voffsets = []
    kencoded = []
    vencoded = []
    
    offset = transidx + 8 * msgcount
    for k, v in zip(keys, values):
        kenc = k.encode('utf-8')
        venc = v.encode('utf-8')
        kencoded.append(kenc)
        vencoded.append(venc)
        koffsets.append((len(kenc), offset))
        offset += len(kenc) + 1
        voffsets.append((len(venc), offset))
        offset += len(venc) + 1
    
    # Create the output
    output = struct.pack('<I', magic)
    output += struct.pack('<I', version)
    output += struct.pack('<I', msgcount)
    output += struct.pack('<I', masteridx)
    output += struct.pack('<I', transidx)
    output += struct.pack('<I', 0)  # hash table offset
    output += struct.pack('<I', 0)  # hash table size
    
    # Add key offsets
    for length, offset in koffsets:
        output += struct.pack('<I', length)
        output += struct.pack('<I', offset)
    
    # Add value offsets  
    for length, offset in voffsets:
        output += struct.pack('<I', length)
        output += struct.pack('<I', offset)
    
    # Add strings
    for kenc in kencoded:
        output += kenc + b'\x00'
    for venc in vencoded:
        output += venc + b'\x00'
    
    # Write to file
    os.makedirs('locale/ar/LC_MESSAGES', exist_ok=True)
    with open('locale/ar/LC_MESSAGES/django.mo', 'wb') as f:
        f.write(output)
    
    print(f"Created django.mo with {msgcount} translations")

if __name__ == '__main__':
    create_mo_file()
