{% extends 'base.html' %}
{% load i18n crispy_forms_tags %}

{% block title %}{% trans "Sales Report" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{% trans "Sales Report" %}</h1>
            <p class="text-muted">{% trans "Analyze sales performance and trends" %}</p>
        </div>
        <div class="btn-group" role="group">
            {% if report_data %}
                <a href="{% url 'reports:export_report' 'sales' %}?{{ request.GET.urlencode }}&format=csv" 
                   class="btn btn-outline-primary">
                    <i class="fas fa-download me-2"></i>{% trans "Export CSV" %}
                </a>
            {% endif %}
            <a href="{% url 'reports:reports_home' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Reports" %}
            </a>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-filter me-2"></i>{% trans "Report Filters" %}
            </h6>
        </div>
        <div class="card-body">
            <form method="get">
                {% crispy form %}
            </form>
        </div>
    </div>
    
    {% if report_data %}
        <!-- Summary Statistics -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">${{ report_data.summary.total_revenue|floatformat:2|default:"0.00" }}</h4>
                                <p class="mb-0">{% trans "Total Revenue" %}</p>
                            </div>
                            <div>
                                <i class="fas fa-dollar-sign fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">{{ report_data.summary.total_invoices|default:"0" }}</h4>
                                <p class="mb-0">{% trans "Total Invoices" %}</p>
                            </div>
                            <div>
                                <i class="fas fa-file-invoice fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">${{ report_data.summary.average_invoice|floatformat:2|default:"0.00" }}</h4>
                                <p class="mb-0">{% trans "Average Invoice" %}</p>
                            </div>
                            <div>
                                <i class="fas fa-chart-bar fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">${{ report_data.summary.total_tax|floatformat:2|default:"0.00" }}</h4>
                                <p class="mb-0">{% trans "Total Tax" %}</p>
                            </div>
                            <div>
                                <i class="fas fa-percentage fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Sales Trend Chart -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>{% trans "Sales Trend" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        <canvas id="salesChart" height="100"></canvas>
                    </div>
                </div>
                
                <!-- Top Products -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-trophy me-2"></i>{% trans "Top Products" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if report_data.top_products %}
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>{% trans "Product" %}</th>
                                            <th class="text-end">{% trans "Quantity Sold" %}</th>
                                            <th class="text-end">{% trans "Revenue" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for product in report_data.top_products %}
                                            <tr>
                                                <td>{{ product.product__name }}</td>
                                                <td class="text-end">{{ product.total_quantity }}</td>
                                                <td class="text-end">${{ product.total_revenue|floatformat:2 }}</td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-muted text-center">{% trans "No product data available for this period" %}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Top Customers & Payment Methods -->
            <div class="col-lg-4">
                <!-- Top Customers -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-users me-2"></i>{% trans "Top Customers" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if report_data.top_customers %}
                            {% for customer in report_data.top_customers %}
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <div class="fw-bold">{{ customer.customer__name|truncatechars:20 }}</div>
                                        <small class="text-muted">{{ customer.invoice_count }} {% trans "invoices" %}</small>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold text-primary">${{ customer.total_sales|floatformat:2 }}</div>
                                    </div>
                                </div>
                                {% if not forloop.last %}<hr class="my-2">{% endif %}
                            {% endfor %}
                        {% else %}
                            <p class="text-muted text-center">{% trans "No customer data available" %}</p>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Payment Methods -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-credit-card me-2"></i>{% trans "Payment Methods" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if report_data.payment_methods %}
                            <canvas id="paymentChart" height="200"></canvas>
                        {% else %}
                            <p class="text-muted text-center">{% trans "No payment data available" %}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Invoices -->
        {% if report_data.invoices %}
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-list me-2"></i>{% trans "Recent Invoices" %}
                </h6>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>{% trans "Invoice #" %}</th>
                                <th>{% trans "Customer" %}</th>
                                <th>{% trans "Date" %}</th>
                                <th>{% trans "Amount" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Sales Person" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in report_data.invoices %}
                                <tr>
                                    <td>
                                        <a href="{% url 'invoices:invoice_detail' invoice.pk %}">
                                            {{ invoice.invoice_number }}
                                        </a>
                                    </td>
                                    <td>{{ invoice.customer.name }}</td>
                                    <td>{{ invoice.invoice_date|date:"M d, Y" }}</td>
                                    <td>${{ invoice.total_amount }}</td>
                                    <td>
                                        <span class="badge bg-{% if invoice.status == 'paid' %}success{% elif invoice.status == 'pending' %}warning{% else %}danger{% endif %}">
                                            {{ invoice.get_status_display }}
                                        </span>
                                    </td>
                                    <td>{{ invoice.created_by.get_full_name|default:invoice.created_by.username }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
        
    {% else %}
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-chart-line text-muted" style="font-size: 3rem;"></i>
                <h5 class="mt-3 text-muted">{% trans "Generate Sales Report" %}</h5>
                <p class="text-muted">{% trans "Select your filters above and click 'Generate Report' to view sales analytics." %}</p>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
{% if report_data %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Sales Trend Chart
    const salesData = {{ report_data.daily_sales|safe }};
    const salesLabels = Object.keys(salesData);
    const salesValues = Object.values(salesData);
    
    const salesCtx = document.getElementById('salesChart').getContext('2d');
    new Chart(salesCtx, {
        type: 'line',
        data: {
            labels: salesLabels,
            datasets: [{
                label: 'Daily Sales',
                data: salesValues,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toFixed(2);
                        }
                    }
                }
            }
        }
    });
    
    // Payment Methods Chart
    {% if report_data.payment_methods %}
    const paymentData = [
        {% for method in report_data.payment_methods %}
            {
                label: '{{ method.get_payment_method_display }}',
                value: {{ method.total|default:0 }}
            }{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];
    
    const paymentCtx = document.getElementById('paymentChart').getContext('2d');
    new Chart(paymentCtx, {
        type: 'doughnut',
        data: {
            labels: paymentData.map(item => item.label),
            datasets: [{
                data: paymentData.map(item => item.value),
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    {% endif %}
});
</script>
{% endif %}
{% endblock %}
