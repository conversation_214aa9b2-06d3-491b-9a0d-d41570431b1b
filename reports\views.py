from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse, HttpResponse
from django.db.models import Sum, Count, Avg, F, Q
from django.utils import timezone
from datetime import datetime, timedelta
import json
import csv
from decimal import Decimal
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from io import BytesIO

from .forms import SalesReportForm, InventoryReportForm, FinancialReportForm, DashboardFilterForm
from invoices.models import Invoice, InvoiceItem, Payment
from products.models import Product, Category
from customers.models import Customer
from accounts.models import User


def can_view_reports(user):
    """Check if user can view reports"""
    return user.is_authenticated


def can_view_financial_reports(user):
    """Check if user can view financial reports"""
    return user.is_authenticated and user.role in [User.UserRole.ADMIN, User.UserRole.ACCOUNTANT]


@login_required
def reports_home(request):
    """Reports dashboard home"""
    context = {
        'can_view_financial': can_view_financial_reports(request.user),
    }
    return render(request, 'reports/reports_home.html', context)


@login_required
@user_passes_test(can_view_reports)
def sales_report(request):
    """Generate sales reports"""
    form = SalesReportForm(request.GET or None)
    report_data = None

    if form.is_valid():
        start_date, end_date = form.get_date_range()
        customer = form.cleaned_data.get('customer')
        sales_person = form.cleaned_data.get('sales_person')

        # Base queryset
        invoices = Invoice.objects.filter(
            invoice_date__range=[start_date, end_date],
            status__in=['paid', 'pending']
        ).select_related('customer', 'created_by')

        # Apply filters
        if customer:
            invoices = invoices.filter(customer=customer)

        if sales_person:
            invoices = invoices.filter(created_by=sales_person)

        # Calculate summary statistics
        summary = invoices.aggregate(
            total_invoices=Count('id'),
            total_revenue=Sum('total_amount'),
            average_invoice=Avg('total_amount'),
            total_tax=Sum('tax_amount'),
            total_discount=Sum('discount_amount')
        )

        # Sales by day
        daily_sales = {}
        current_date = start_date
        while current_date <= end_date:
            daily_total = invoices.filter(invoice_date=current_date).aggregate(
                total=Sum('total_amount')
            )['total'] or 0
            daily_sales[current_date.strftime('%Y-%m-%d')] = float(daily_total)
            current_date += timedelta(days=1)

        # Top customers
        top_customers = invoices.values('customer__name').annotate(
            total_sales=Sum('total_amount'),
            invoice_count=Count('id')
        ).order_by('-total_sales')[:10]

        # Top products
        top_products = InvoiceItem.objects.filter(
            invoice__in=invoices
        ).values('product__name').annotate(
            total_quantity=Sum('quantity'),
            total_revenue=Sum(F('quantity') * F('unit_price'))
        ).order_by('-total_revenue')[:10]

        # Sales by payment method
        payment_methods = invoices.values('payment_method').annotate(
            total=Sum('total_amount'),
            count=Count('id')
        ).order_by('-total')

        report_data = {
            'summary': summary,
            'daily_sales': daily_sales,
            'top_customers': top_customers,
            'top_products': top_products,
            'payment_methods': payment_methods,
            'period': f"{start_date} to {end_date}",
            'invoices': invoices[:50]  # Latest 50 invoices for detail view
        }

    context = {
        'form': form,
        'report_data': report_data,
    }

    return render(request, 'reports/sales_report.html', context)


@login_required
@user_passes_test(can_view_reports)
def inventory_report(request):
    """Generate inventory reports"""
    form = InventoryReportForm(request.GET or None)
    report_data = None

    if form.is_valid():
        report_type = form.cleaned_data.get('report_type')
        category = form.cleaned_data.get('category')
        include_inactive = form.cleaned_data.get('include_inactive')

        # Base queryset
        products = Product.objects.select_related('category')

        if not include_inactive:
            products = products.filter(is_active=True)

        if category:
            products = products.filter(category=category)

        if report_type == 'current_stock':
            report_data = {
                'products': products.order_by('name'),
                'total_products': products.count(),
                'total_stock_value': products.aggregate(
                    total=Sum(F('stock_quantity') * F('cost_price'))
                )['total'] or 0,
                'total_retail_value': products.aggregate(
                    total=Sum(F('stock_quantity') * F('selling_price'))
                )['total'] or 0,
            }

        elif report_type == 'low_stock':
            low_stock_products = products.filter(stock_quantity__lte=F('min_stock_level'))
            report_data = {
                'products': low_stock_products.order_by('stock_quantity'),
                'total_low_stock': low_stock_products.count(),
            }

        elif report_type == 'out_of_stock':
            out_of_stock_products = products.filter(stock_quantity=0)
            report_data = {
                'products': out_of_stock_products.order_by('name'),
                'total_out_of_stock': out_of_stock_products.count(),
            }

        elif report_type == 'valuation':
            # Calculate inventory valuation
            valuation_data = []
            total_cost_value = 0
            total_retail_value = 0

            for product in products:
                cost_value = product.stock_quantity * product.cost_price
                retail_value = product.stock_quantity * product.selling_price
                profit_potential = retail_value - cost_value

                valuation_data.append({
                    'product': product,
                    'cost_value': cost_value,
                    'retail_value': retail_value,
                    'profit_potential': profit_potential,
                })

                total_cost_value += cost_value
                total_retail_value += retail_value

            report_data = {
                'valuation_data': sorted(valuation_data, key=lambda x: x['retail_value'], reverse=True),
                'total_cost_value': total_cost_value,
                'total_retail_value': total_retail_value,
                'total_profit_potential': total_retail_value - total_cost_value,
            }

        # Add category breakdown
        category_breakdown = products.values('category__name').annotate(
            product_count=Count('id'),
            total_stock=Sum('stock_quantity'),
            total_value=Sum(F('stock_quantity') * F('selling_price'))
        ).order_by('-total_value')

        if report_data:
            report_data['category_breakdown'] = category_breakdown
            report_data['report_type'] = report_type

    context = {
        'form': form,
        'report_data': report_data,
    }

    return render(request, 'reports/inventory_report.html', context)


@login_required
@user_passes_test(can_view_financial_reports)
def financial_report(request):
    """Generate financial reports"""
    form = FinancialReportForm(request.GET or None)
    report_data = None

    if form.is_valid():
        report_type = form.cleaned_data.get('report_type')
        start_date, end_date = form.get_date_range()

        if report_type == 'profit_loss':
            # Calculate profit and loss
            invoices = Invoice.objects.filter(
                invoice_date__range=[start_date, end_date],
                status='paid'
            )

            # Revenue
            total_revenue = invoices.aggregate(total=Sum('total_amount'))['total'] or 0
            total_tax = invoices.aggregate(total=Sum('tax_amount'))['total'] or 0
            total_discount = invoices.aggregate(total=Sum('discount_amount'))['total'] or 0

            # Cost of goods sold
            invoice_items = InvoiceItem.objects.filter(invoice__in=invoices)
            cogs = sum(item.quantity * item.product.cost_price for item in invoice_items)

            # Gross profit
            gross_profit = total_revenue - cogs
            gross_margin = (gross_profit / total_revenue * 100) if total_revenue > 0 else 0

            # Net profit (simplified - would include operating expenses in real scenario)
            net_profit = gross_profit
            net_margin = (net_profit / total_revenue * 100) if total_revenue > 0 else 0

            report_data = {
                'total_revenue': total_revenue,
                'total_tax': total_tax,
                'total_discount': total_discount,
                'cogs': cogs,
                'gross_profit': gross_profit,
                'gross_margin': gross_margin,
                'net_profit': net_profit,
                'net_margin': net_margin,
                'period': f"{start_date} to {end_date}",
            }

        elif report_type == 'revenue_summary':
            # Revenue breakdown
            invoices = Invoice.objects.filter(
                invoice_date__range=[start_date, end_date]
            )

            # Revenue by status
            revenue_by_status = invoices.values('status').annotate(
                total=Sum('total_amount'),
                count=Count('id')
            ).order_by('-total')

            # Revenue by payment method
            revenue_by_payment = invoices.values('payment_method').annotate(
                total=Sum('total_amount'),
                count=Count('id')
            ).order_by('-total')

            # Monthly revenue trend
            monthly_revenue = {}
            current_date = start_date.replace(day=1)
            while current_date <= end_date:
                month_end = (current_date.replace(month=current_date.month + 1, day=1) - timedelta(days=1)) if current_date.month < 12 else current_date.replace(month=12, day=31)
                month_total = invoices.filter(
                    invoice_date__range=[current_date, min(month_end, end_date)]
                ).aggregate(total=Sum('total_amount'))['total'] or 0

                monthly_revenue[current_date.strftime('%Y-%m')] = float(month_total)

                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)

                if current_date > end_date:
                    break

            report_data = {
                'revenue_by_status': revenue_by_status,
                'revenue_by_payment': revenue_by_payment,
                'monthly_revenue': monthly_revenue,
                'total_revenue': invoices.aggregate(total=Sum('total_amount'))['total'] or 0,
                'period': f"{start_date} to {end_date}",
            }

        elif report_type == 'outstanding_invoices':
            # Outstanding invoices
            outstanding = Invoice.objects.filter(
                status__in=['pending', 'overdue']
            ).select_related('customer').order_by('due_date')

            total_outstanding = outstanding.aggregate(total=Sum('total_amount'))['total'] or 0

            # Age analysis
            today = timezone.now().date()
            age_analysis = {
                'current': outstanding.filter(due_date__gte=today).aggregate(total=Sum('total_amount'))['total'] or 0,
                '1_30_days': outstanding.filter(due_date__lt=today, due_date__gte=today - timedelta(days=30)).aggregate(total=Sum('total_amount'))['total'] or 0,
                '31_60_days': outstanding.filter(due_date__lt=today - timedelta(days=30), due_date__gte=today - timedelta(days=60)).aggregate(total=Sum('total_amount'))['total'] or 0,
                '60_plus_days': outstanding.filter(due_date__lt=today - timedelta(days=60)).aggregate(total=Sum('total_amount'))['total'] or 0,
            }

            report_data = {
                'outstanding_invoices': outstanding,
                'total_outstanding': total_outstanding,
                'age_analysis': age_analysis,
            }

    context = {
        'form': form,
        'report_data': report_data,
    }

    return render(request, 'reports/financial_report.html', context)


@login_required
def dashboard_analytics(request):
    """Dashboard analytics data for charts"""
    form = DashboardFilterForm(request.GET or None)
    days = int(request.GET.get('period', 30))

    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=days)

    # Sales trend
    sales_data = []
    current_date = start_date
    while current_date <= end_date:
        daily_sales = Invoice.objects.filter(
            invoice_date=current_date,
            status__in=['paid', 'pending']
        ).aggregate(total=Sum('total_amount'))['total'] or 0

        sales_data.append({
            'date': current_date.strftime('%Y-%m-%d'),
            'sales': float(daily_sales)
        })
        current_date += timedelta(days=1)

    # Top products by revenue
    top_products = InvoiceItem.objects.filter(
        invoice__invoice_date__range=[start_date, end_date],
        invoice__status__in=['paid', 'pending']
    ).values('product__name').annotate(
        revenue=Sum(F('quantity') * F('unit_price'))
    ).order_by('-revenue')[:10]

    # Customer distribution
    customer_data = Invoice.objects.filter(
        invoice_date__range=[start_date, end_date],
        status__in=['paid', 'pending']
    ).values('customer__name').annotate(
        total=Sum('total_amount')
    ).order_by('-total')[:10]

    # Payment method distribution
    payment_data = Invoice.objects.filter(
        invoice_date__range=[start_date, end_date],
        status__in=['paid', 'pending']
    ).values('payment_method').annotate(
        total=Sum('total_amount'),
        count=Count('id')
    ).order_by('-total')

    # Summary statistics
    summary = {
        'total_sales': Invoice.objects.filter(
            invoice_date__range=[start_date, end_date],
            status__in=['paid', 'pending']
        ).aggregate(total=Sum('total_amount'))['total'] or 0,

        'total_invoices': Invoice.objects.filter(
            invoice_date__range=[start_date, end_date]
        ).count(),

        'total_customers': Customer.objects.filter(
            invoices__invoice_date__range=[start_date, end_date]
        ).distinct().count(),

        'average_invoice': Invoice.objects.filter(
            invoice_date__range=[start_date, end_date],
            status__in=['paid', 'pending']
        ).aggregate(avg=Avg('total_amount'))['avg'] or 0,
    }

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'sales_data': sales_data,
            'top_products': list(top_products),
            'customer_data': list(customer_data),
            'payment_data': list(payment_data),
            'summary': summary,
        })

    context = {
        'form': form,
        'sales_data': json.dumps(sales_data),
        'top_products': top_products,
        'customer_data': customer_data,
        'payment_data': payment_data,
        'summary': summary,
    }

    return render(request, 'reports/dashboard_analytics.html', context)


@login_required
@user_passes_test(can_view_reports)
def export_report(request, report_type):
    """Export reports to CSV or PDF"""
    format_type = request.GET.get('format', 'csv')

    if report_type == 'sales':
        form = SalesReportForm(request.GET)
        if form.is_valid():
            start_date, end_date = form.get_date_range()
            invoices = Invoice.objects.filter(
                invoice_date__range=[start_date, end_date],
                status__in=['paid', 'pending']
            ).select_related('customer', 'created_by')

            if format_type == 'csv':
                response = HttpResponse(content_type='text/csv')
                response['Content-Disposition'] = f'attachment; filename="sales_report_{start_date}_{end_date}.csv"'

                writer = csv.writer(response)
                writer.writerow([
                    'Invoice Number', 'Customer', 'Date', 'Amount', 'Status',
                    'Payment Method', 'Sales Person'
                ])

                for invoice in invoices:
                    writer.writerow([
                        invoice.invoice_number,
                        invoice.customer.name,
                        invoice.invoice_date,
                        invoice.total_amount,
                        invoice.get_status_display(),
                        invoice.get_payment_method_display(),
                        invoice.created_by.get_full_name() or invoice.created_by.username
                    ])

                return response

    elif report_type == 'inventory':
        form = InventoryReportForm(request.GET)
        if form.is_valid():
            products = Product.objects.select_related('category')

            if format_type == 'csv':
                response = HttpResponse(content_type='text/csv')
                response['Content-Disposition'] = 'attachment; filename="inventory_report.csv"'

                writer = csv.writer(response)
                writer.writerow([
                    'Product Name', 'SKU', 'Category', 'Stock Quantity',
                    'Min Stock Level', 'Cost Price', 'Selling Price', 'Status'
                ])

                for product in products:
                    writer.writerow([
                        product.name,
                        product.sku,
                        product.category.name if product.category else '',
                        product.stock_quantity,
                        product.min_stock_level,
                        product.cost_price,
                        product.selling_price,
                        'Active' if product.is_active else 'Inactive'
                    ])

                return response

    # Default redirect if export fails
    messages.error(request, _('Export failed. Please try again.'))
    return redirect('reports:reports_home')
