from django.conf import settings
from django.utils import translation


def company_info(request):
    """
    Context processor to provide company information to all templates
    """
    return {
        'COMPANY_NAME': getattr(settings, 'COMPANY_NAME', 'شركة الفواتير القطرية'),
        'COMPANY_ADDRESS': getattr(settings, 'COMPANY_ADDRESS', 'الدوحة، قطر'),
        'COMPANY_PHONE': getattr(settings, 'COMPANY_PHONE', '+974 4444 4444'),
        'COMPANY_EMAIL': getattr(settings, 'COMPANY_EMAIL', '<EMAIL>'),
        'COMPANY_TAX_ID': getattr(settings, 'COMPANY_TAX_ID', 'QA123456789'),
        'COMPANY_COUNTRY': getattr(settings, 'COMPANY_COUNTRY', 'قطر'),
        'COMPANY_CURRENCY': getattr(settings, 'COMPANY_CURRENCY', 'QAR'),
        'COMPANY_CURRENCY_SYMBOL': getattr(settings, 'COMPANY_CURRENCY_SYMBOL', 'ر.ق'),
    }


def language_info(request):
    """
    Context processor to provide language information to all templates
    """
    current_language = translation.get_language()
    return {
        'CURRENT_LANGUAGE': current_language,
        'LANGUAGE_CODE': current_language,
        'LANGUAGES': settings.LANGUAGES,
        'RTL_LANGUAGES': ['ar', 'he', 'fa', 'ur'],
        'IS_RTL': current_language in ['ar', 'he', 'fa', 'ur'],
    }
