from django.conf import settings


def company_info(request):
    """
    Context processor to provide company information to all templates
    """
    return {
        'COMPANY_NAME': getattr(settings, 'COMPANY_NAME', 'Your Company Name'),
        'COMPANY_ADDRESS': getattr(settings, 'COMPANY_ADDRESS', 'Your Company Address'),
        'COMPANY_PHONE': getattr(settings, 'COMPANY_PHONE', '+*********0'),
        'COMPANY_EMAIL': getattr(settings, 'COMPANY_EMAIL', '<EMAIL>'),
        'COMPANY_TAX_ID': getattr(settings, 'COMPANY_TAX_ID', '*********'),
    }
