from django.shortcuts import redirect
from django.utils import translation
from django.conf import settings
from django.http import HttpResponseRedirect
from django.urls import reverse
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt

@require_POST
@csrf_exempt
def set_language(request):
    """
    Set the language preference and redirect back to the previous page
    """
    language = request.POST.get('language')
    next_url = request.POST.get('next', '/')
    
    if language and language in [lang[0] for lang in settings.LANGUAGES]:
        # Activate the language
        translation.activate(language)
        
        # Set language in session
        request.session['_language'] = language
        
        # Set language cookie
        response = HttpResponseRedirect(next_url)
        response.set_cookie(
            settings.LANGUAGE_COOKIE_NAME, 
            language,
            max_age=settings.LANGUAGE_COOKIE_AGE,
            path=settings.LANGUAGE_COOKIE_PATH,
            domain=settings.LANGUAGE_COOKIE_DOMAIN,
            secure=settings.LANGUAGE_COOKIE_SECURE,
            httponly=settings.LANGUAGE_COOKIE_HTTPONLY,
            samesite=settings.LANGUAGE_COOKIE_SAMESITE,
        )
        return response
    
    return redirect(next_url)
