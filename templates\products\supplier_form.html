{% extends 'base.html' %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}
    {% if object %}
        {% trans "Edit Supplier" %} - {{ object.name }}
    {% else %}
        {% trans "Add New Supplier" %}
    {% endif %}
    - {{ block.super }}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                {% if object %}
                    {% trans "Edit Supplier" %}
                {% else %}
                    {% trans "Add New Supplier" %}
                {% endif %}
            </h1>
            <p class="text-muted">
                {% if object %}
                    {% trans "Update supplier information" %}
                {% else %}
                    {% trans "Add a new supplier to your system" %}
                {% endif %}
            </p>
        </div>
        <div>
            <a href="{% url 'products:supplier_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Suppliers" %}
            </a>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-8">
            <form method="post" class="needs-validation" novalidate>
                {% csrf_token %}
                
                <!-- Basic Information -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>{% trans "Basic Information" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">
                                        {{ form.name.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.name|add_class:"form-control" }}
                                    {% if form.name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.name.errors.0 }}
                                        </div>
                                    {% endif %}
                                    {% if form.name.help_text %}
                                        <div class="form-text">{{ form.name.help_text }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.company_registration.id_for_label }}" class="form-label">
                                        {{ form.company_registration.label }}
                                    </label>
                                    {{ form.company_registration|add_class:"form-control" }}
                                    {% if form.company_registration.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.company_registration.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.tax_id.id_for_label }}" class="form-label">
                                        {{ form.tax_id.label }}
                                    </label>
                                    {{ form.tax_id|add_class:"form-control" }}
                                    {% if form.tax_id.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.tax_id.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        {{ form.is_active|add_class:"form-check-input" }}
                                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                            {{ form.is_active.label }}
                                        </label>
                                    </div>
                                    {% if form.is_active.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.is_active.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Contact Information -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-address-book me-2"></i>{% trans "Contact Information" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.contact_person.id_for_label }}" class="form-label">
                                        {{ form.contact_person.label }}
                                    </label>
                                    {{ form.contact_person|add_class:"form-control" }}
                                    {% if form.contact_person.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.contact_person.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.phone.id_for_label }}" class="form-label">
                                        {{ form.phone.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.phone|add_class:"form-control" }}
                                    {% if form.phone.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.phone.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.email.id_for_label }}" class="form-label">
                                        {{ form.email.label }}
                                    </label>
                                    {{ form.email|add_class:"form-control" }}
                                    {% if form.email.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.email.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.website.id_for_label }}" class="form-label">
                                        {{ form.website.label }}
                                    </label>
                                    {{ form.website|add_class:"form-control" }}
                                    {% if form.website.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.website.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Address Information -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-map-marker-alt me-2"></i>{% trans "Address Information" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="{{ form.address.id_for_label }}" class="form-label">
                                {{ form.address.label }}
                            </label>
                            {{ form.address|add_class:"form-control" }}
                            {% if form.address.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.address.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.city.id_for_label }}" class="form-label">
                                        {{ form.city.label }}
                                    </label>
                                    {{ form.city|add_class:"form-control" }}
                                    {% if form.city.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.city.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.country.id_for_label }}" class="form-label">
                                        {{ form.country.label }} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.country|add_class:"form-control" }}
                                    {% if form.country.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.country.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Additional Information -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-sticky-note me-2"></i>{% trans "Additional Information" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                {{ form.notes.label }}
                            </label>
                            {{ form.notes|add_class:"form-control" }}
                            {% if form.notes.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.notes.errors.0 }}
                                </div>
                            {% endif %}
                            {% if form.notes.help_text %}
                                <div class="form-text">{{ form.notes.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Form Actions -->
                <div class="card mt-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                {% if object %}
                                    <a href="{% url 'products:supplier_delete' object.pk %}" 
                                       class="btn btn-outline-danger"
                                       onclick="return confirm('{% trans 'Are you sure you want to delete this supplier?' %}')">
                                        <i class="fas fa-trash me-2"></i>{% trans "Delete Supplier" %}
                                    </a>
                                {% endif %}
                            </div>
                            <div>
                                <a href="{% url 'products:supplier_list' %}" class="btn btn-outline-secondary me-2">
                                    {% trans "Cancel" %}
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    {% if object %}
                                        {% trans "Update Supplier" %}
                                    {% else %}
                                        {% trans "Create Supplier" %}
                                    {% endif %}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Help Card -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>{% trans "Help" %}
                    </h6>
                </div>
                <div class="card-body">
                    <h6>{% trans "Required Fields" %}</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success me-1"></i> {% trans "Supplier Name" %}</li>
                        <li><i class="fas fa-check text-success me-1"></i> {% trans "Phone Number" %}</li>
                        <li><i class="fas fa-check text-success me-1"></i> {% trans "Country" %}</li>
                    </ul>
                    
                    <h6 class="mt-3">{% trans "Tips" %}</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-lightbulb text-warning me-1"></i> {% trans "Use clear, descriptive names" %}</li>
                        <li><i class="fas fa-lightbulb text-warning me-1"></i> {% trans "Include contact person for better communication" %}</li>
                        <li><i class="fas fa-lightbulb text-warning me-1"></i> {% trans "Add notes for special terms or conditions" %}</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
