{% extends 'base.html' %}
{% load i18n crispy_forms_tags %}

{% block title %}{% trans "Change Password" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <!-- <PERSON> Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">{% trans "Change Password" %}</h1>
                    <p class="text-muted">{% trans "Update your account password" %}</p>
                </div>
                <a href="{% url 'accounts:profile' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Profile" %}
                </a>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-key me-2"></i>{% trans "Password Change" %}
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.old_password.id_for_label }}" class="form-label">
                                {% trans "Current Password" %}
                            </label>
                            {{ form.old_password }}
                            {% if form.old_password.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.old_password.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.new_password1.id_for_label }}" class="form-label">
                                {% trans "New Password" %}
                            </label>
                            {{ form.new_password1 }}
                            {% if form.new_password1.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.new_password1.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                {% trans "Your password must contain at least 8 characters and cannot be entirely numeric." %}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.new_password2.id_for_label }}" class="form-label">
                                {% trans "Confirm New Password" %}
                            </label>
                            {{ form.new_password2 }}
                            {% if form.new_password2.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.new_password2.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors.0 }}
                            </div>
                        {% endif %}
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'accounts:profile' %}" class="btn btn-secondary me-md-2">
                                {% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{% trans "Change Password" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Security Tips -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>{% trans "Security Tips" %}
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            {% trans "Use a strong password with at least 8 characters" %}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            {% trans "Include uppercase and lowercase letters, numbers, and symbols" %}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            {% trans "Don't use personal information in your password" %}
                        </li>
                        <li>
                            <i class="fas fa-check text-success me-2"></i>
                            {% trans "Change your password regularly" %}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
