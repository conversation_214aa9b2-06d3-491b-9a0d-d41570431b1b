# Generated by Django 4.2.7 on 2025-06-17 09:45

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Customer",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "customer_type",
                    models.CharField(
                        choices=[
                            ("individual", "Individual"),
                            ("business", "Business"),
                        ],
                        default="individual",
                        max_length=20,
                        verbose_name="Customer Type",
                    ),
                ),
                (
                    "name",
                    models.Char<PERSON>ield(max_length=200, verbose_name="Customer Name"),
                ),
                (
                    "company_name",
                    models.CharField(
                        blank=True,
                        max_length=200,
                        null=True,
                        verbose_name="Company Name",
                    ),
                ),
                ("phone", models.CharField(max_length=20, verbose_name="Phone Number")),
                (
                    "email",
                    models.EmailField(
                        blank=True,
                        max_length=254,
                        null=True,
                        verbose_name="Email Address",
                    ),
                ),
                (
                    "address",
                    models.TextField(blank=True, null=True, verbose_name="Address"),
                ),
                (
                    "tax_id",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="Tax ID"
                    ),
                ),
                (
                    "credit_limit",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Credit Limit",
                    ),
                ),
                (
                    "payment_terms",
                    models.PositiveIntegerField(
                        default=30,
                        help_text="Payment terms in days",
                        verbose_name="Payment Terms (Days)",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="Active Status"),
                ),
                (
                    "notes",
                    models.TextField(blank=True, null=True, verbose_name="Notes"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
            ],
            options={
                "verbose_name": "Customer",
                "verbose_name_plural": "Customers",
                "ordering": ["name"],
            },
        ),
    ]
