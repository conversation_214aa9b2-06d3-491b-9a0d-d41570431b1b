/* Sales Invoice Management System - Custom Styles */

/* Font Configuration */
.ltr-layout {
    font-family: 'Inter', sans-serif;
}

.rtl-layout {
    font-family: 'Cairo', sans-serif;
}

/* Layout Styles */
body {
    background-color: #f8f9fa;
    min-height: 100vh;
}

.main-content {
    min-height: calc(100vh - 140px);
    padding-bottom: 2rem;
}

.auth-content {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Navigation Styles */
.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.375rem;
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 0.375rem;
}

/* Card Styles */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Dashboard Cards */
.dashboard-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 1rem;
}

.dashboard-card .card-body {
    padding: 2rem;
}

.dashboard-card .display-4 {
    font-weight: 700;
}

/* Statistics Cards */
.stat-card {
    border-left: 4px solid;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
}

.stat-card.primary {
    border-left-color: #0d6efd;
}

.stat-card.success {
    border-left-color: #198754;
}

.stat-card.warning {
    border-left-color: #ffc107;
}

.stat-card.danger {
    border-left-color: #dc3545;
}

.stat-card.info {
    border-left-color: #0dcaf0;
}

/* Form Styles */
.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
    background-color: #667eea;
    border-color: #667eea;
}

.btn-primary:hover {
    background-color: #5a6fd8;
    border-color: #5a6fd8;
}

/* Authentication Styles */
.auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 1rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.auth-logo {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

/* Table Styles */
.table {
    background-color: white;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

/* Badge Styles */
.badge {
    font-size: 0.75em;
    font-weight: 500;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* RTL Specific Styles */
.rtl-layout .dropdown-menu {
    text-align: right;
}

.rtl-layout .navbar-nav .dropdown-menu {
    right: 0;
    left: auto;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }
    
    .navbar-brand {
        font-size: 1rem;
    }
    
    .dashboard-card .card-body {
        padding: 1.5rem;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .footer,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .main-content {
        margin-top: 0 !important;
        padding-top: 0 !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
