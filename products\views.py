from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import CreateView, UpdateView, ListView, DeleteView, DetailView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q, F
from django.db import models
from django.conf import settings
import csv
import openpyxl
from io import TextIOWrapper
from .models import Product, Category, Supplier
from .forms import ProductForm, CategoryForm, SupplierForm, ProductSearchForm, BulkProductImportForm
from accounts.models import User


def can_manage_products(user):
    """Check if user can manage products"""
    return user.is_authenticated and user.role in [User.UserRole.ADMIN, User.UserRole.SALES_EMPLOYEE]


class ProductManagementMixin(UserPassesTestMixin):
    """Mixin to require product management permissions"""
    def test_func(self):
        return can_manage_products(self.request.user)


@login_required
@user_passes_test(can_manage_products)
def product_list(request):
    """List all products with search and filtering"""
    form = ProductSearchForm(request.GET)
    products = Product.objects.select_related('category').order_by('-created_at')

    if form.is_valid():
        search = form.cleaned_data.get('search')
        category = form.cleaned_data.get('category')
        stock_status = form.cleaned_data.get('stock_status')
        is_active = form.cleaned_data.get('is_active')

        if search:
            products = products.filter(
                Q(name__icontains=search) |
                Q(sku__icontains=search) |
                Q(description__icontains=search)
            )

        if category:
            products = products.filter(category=category)

        if stock_status:
            if stock_status == 'in_stock':
                products = products.filter(stock_quantity__gt=F('min_stock_level'))
            elif stock_status == 'low_stock':
                products = products.filter(stock_quantity__lte=F('min_stock_level'), stock_quantity__gt=0)
            elif stock_status == 'out_of_stock':
                products = products.filter(stock_quantity=0)

        if is_active:
            products = products.filter(is_active=is_active == 'true')

    # Pagination
    paginator = Paginator(products, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'products': page_obj,
        'form': form,
        'is_paginated': page_obj.has_other_pages(),
        'page_obj': page_obj,
    }

    return render(request, 'products/product_list.html', context)


class ProductCreateView(ProductManagementMixin, CreateView):
    """Create new product"""
    model = Product
    form_class = ProductForm
    template_name = 'products/product_create.html'
    success_url = reverse_lazy('products:product_list')

    def form_valid(self, form):
        messages.success(self.request, _('Product created successfully.'))
        return super().form_valid(form)


class ProductUpdateView(ProductManagementMixin, UpdateView):
    """Update existing product"""
    model = Product
    form_class = ProductForm
    template_name = 'products/product_update.html'
    success_url = reverse_lazy('products:product_list')

    def form_valid(self, form):
        messages.success(self.request, _('Product updated successfully.'))
        return super().form_valid(form)


class ProductDeleteView(ProductManagementMixin, DeleteView):
    """Delete product"""
    model = Product
    template_name = 'products/product_confirm_delete.html'
    success_url = reverse_lazy('products:product_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Product deleted successfully.'))
        return super().delete(request, *args, **kwargs)


@login_required
@user_passes_test(can_manage_products)
def product_detail(request, pk):
    """Product detail view"""
    product = get_object_or_404(Product, pk=pk)
    return render(request, 'products/product_detail.html', {'product': product})


@login_required
@user_passes_test(can_manage_products)
def generate_barcode(request, pk):
    """Generate barcode for product"""
    product = get_object_or_404(Product, pk=pk)

    try:
        product.generate_barcode()
        product.save()
        messages.success(request, _('Barcode generated successfully.'))
    except Exception as e:
        messages.error(request, _('Error generating barcode: {}').format(str(e)))

    return redirect('products:product_detail', pk=pk)


# Category Views
class CategoryListView(ProductManagementMixin, ListView):
    """List all categories"""
    model = Category
    template_name = 'products/category_list.html'
    context_object_name = 'categories'
    paginate_by = 20
    ordering = ['name']


class CategoryCreateView(ProductManagementMixin, CreateView):
    """Create new category"""
    model = Category
    form_class = CategoryForm
    template_name = 'products/category_create.html'
    success_url = reverse_lazy('products:category_list')

    def form_valid(self, form):
        messages.success(self.request, _('Category created successfully.'))
        return super().form_valid(form)


class CategoryUpdateView(ProductManagementMixin, UpdateView):
    """Update existing category"""
    model = Category
    form_class = CategoryForm
    template_name = 'products/category_update.html'
    success_url = reverse_lazy('products:category_list')

    def form_valid(self, form):
        messages.success(self.request, _('Category updated successfully.'))
        return super().form_valid(form)


# Supplier Views
class SupplierListView(ProductManagementMixin, ListView):
    """List all suppliers"""
    model = Supplier
    template_name = 'products/supplier_list.html'
    context_object_name = 'suppliers'
    paginate_by = 20
    ordering = ['name']


class SupplierCreateView(ProductManagementMixin, CreateView):
    """Create new supplier"""
    model = Supplier
    form_class = SupplierForm
    template_name = 'products/supplier_form.html'
    success_url = reverse_lazy('products:supplier_list')

    def form_valid(self, form):
        messages.success(self.request, _('Supplier created successfully.'))
        return super().form_valid(form)


class SupplierUpdateView(ProductManagementMixin, UpdateView):
    """Update existing supplier"""
    model = Supplier
    form_class = SupplierForm
    template_name = 'products/supplier_form.html'
    success_url = reverse_lazy('products:supplier_list')

    def form_valid(self, form):
        messages.success(self.request, _('Supplier updated successfully.'))
        return super().form_valid(form)


class SupplierDetailView(ProductManagementMixin, DetailView):
    """View supplier details"""
    model = Supplier
    template_name = 'products/supplier_detail.html'
    context_object_name = 'supplier'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        supplier = self.get_object()

        # Calculate additional stats
        products = supplier.products.all()
        context['total_value'] = sum(p.cost_price * p.stock_quantity for p in products)
        context['low_stock_count'] = products.filter(
            stock_quantity__lte=models.F('min_stock_level')
        ).count()

        return context


class SupplierDeleteView(ProductManagementMixin, DeleteView):
    """Delete supplier"""
    model = Supplier
    template_name = 'products/supplier_confirm_delete.html'
    success_url = reverse_lazy('products:supplier_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Supplier deleted successfully.'))
        return super().delete(request, *args, **kwargs)


@login_required
@user_passes_test(can_manage_products)
def bulk_import_products(request):
    """Bulk import products from CSV/Excel"""
    if request.method == 'POST':
        form = BulkProductImportForm(request.POST, request.FILES)
        if form.is_valid():
            file = form.cleaned_data['file']
            update_existing = form.cleaned_data['update_existing']

            try:
                imported_count, updated_count, errors = process_product_import(file, update_existing)

                if errors:
                    for error in errors[:5]:  # Show first 5 errors
                        messages.error(request, error)
                    if len(errors) > 5:
                        messages.warning(request, _('And {} more errors...').format(len(errors) - 5))

                if imported_count > 0:
                    messages.success(request, _('Successfully imported {} products.').format(imported_count))

                if updated_count > 0:
                    messages.success(request, _('Successfully updated {} products.').format(updated_count))

                if imported_count == 0 and updated_count == 0 and not errors:
                    messages.warning(request, _('No products were imported or updated.'))

            except Exception as e:
                messages.error(request, _('Error processing file: {}').format(str(e)))

            return redirect('products:product_list')
    else:
        form = BulkProductImportForm()

    return render(request, 'products/bulk_import.html', {'form': form})


def process_product_import(file, update_existing=False):
    """Process product import from CSV/Excel file"""
    imported_count = 0
    updated_count = 0
    errors = []

    try:
        if file.name.endswith('.csv'):
            # Process CSV file
            decoded_file = file.read().decode('utf-8')
            reader = csv.DictReader(decoded_file.splitlines())
        else:
            # Process Excel file
            workbook = openpyxl.load_workbook(file)
            worksheet = workbook.active
            headers = [cell.value for cell in worksheet[1]]
            reader = []
            for row in worksheet.iter_rows(min_row=2, values_only=True):
                reader.append(dict(zip(headers, row)))

        for row_num, row in enumerate(reader, start=2):
            try:
                # Required fields
                name = row.get('name') or row.get('Name')
                sku = row.get('sku') or row.get('SKU')
                cost_price = row.get('cost_price') or row.get('Cost Price')
                selling_price = row.get('selling_price') or row.get('Selling Price')

                if not all([name, sku, cost_price, selling_price]):
                    errors.append(_('Row {}: Missing required fields (name, sku, cost_price, selling_price)').format(row_num))
                    continue

                # Check if product exists
                product_exists = Product.objects.filter(sku=sku).exists()

                if product_exists and not update_existing:
                    errors.append(_('Row {}: Product with SKU {} already exists').format(row_num, sku))
                    continue

                # Get or create category
                category_name = row.get('category') or row.get('Category')
                category = None
                if category_name:
                    category, _ = Category.objects.get_or_create(
                        name=category_name,
                        defaults={'is_active': True}
                    )

                # Prepare product data
                product_data = {
                    'name': name,
                    'sku': sku,
                    'category': category,
                    'description': row.get('description') or row.get('Description') or '',
                    'cost_price': float(cost_price),
                    'selling_price': float(selling_price),
                    'stock_quantity': int(row.get('stock_quantity') or row.get('Stock Quantity') or 0),
                    'min_stock_level': int(row.get('min_stock_level') or row.get('Min Stock Level') or 10),
                    'is_active': True,
                }

                if product_exists and update_existing:
                    # Update existing product
                    Product.objects.filter(sku=sku).update(**product_data)
                    updated_count += 1
                else:
                    # Create new product
                    Product.objects.create(**product_data)
                    imported_count += 1

            except Exception as e:
                errors.append(_('Row {}: {}').format(row_num, str(e)))
                continue

    except Exception as e:
        errors.append(_('Error reading file: {}').format(str(e)))

    return imported_count, updated_count, errors


@login_required
@user_passes_test(can_manage_products)
def export_products(request):
    """Export products to CSV"""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="products.csv"'

    writer = csv.writer(response)
    writer.writerow([
        'Name', 'SKU', 'Category', 'Description', 'Cost Price', 'Selling Price',
        'Stock Quantity', 'Min Stock Level', 'Active'
    ])

    products = Product.objects.select_related('category').all()
    for product in products:
        writer.writerow([
            product.name,
            product.sku,
            product.category.name if product.category else '',
            product.description,
            product.cost_price,
            product.selling_price,
            product.stock_quantity,
            product.min_stock_level,
            'Yes' if product.is_active else 'No'
        ])

    return response
