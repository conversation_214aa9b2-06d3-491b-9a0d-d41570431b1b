{% extends 'base.html' %}
{% load i18n %}

{% block title %}{{ product.name }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ product.name }}</h1>
            <p class="text-muted">{% trans "Product Details" %}</p>
        </div>
        <div class="btn-group" role="group">
            <a href="{% url 'products:product_update' product.pk %}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>{% trans "Edit Product" %}
            </a>
            <a href="{% url 'products:product_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Products" %}
            </a>
        </div>
    </div>
    
    <div class="row">
        <!-- Product Information -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>{% trans "Product Information" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">{% trans "Name" %}:</td>
                                    <td>{{ product.name }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{% trans "SKU" %}:</td>
                                    <td><code>{{ product.sku }}</code></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{% trans "Category" %}:</td>
                                    <td>
                                        {% if product.category %}
                                            <span class="badge bg-info">{{ product.category.name }}</span>
                                        {% else %}
                                            <span class="text-muted">{% trans "No Category" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{% trans "Status" %}:</td>
                                    <td>
                                        <span class="badge bg-{% if product.is_active %}success{% else %}danger{% endif %}">
                                            {% if product.is_active %}{% trans "Active" %}{% else %}{% trans "Inactive" %}{% endif %}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">{% trans "Cost Price" %}:</td>
                                    <td class="text-success fw-bold">${{ product.cost_price }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{% trans "Selling Price" %}:</td>
                                    <td class="text-primary fw-bold">${{ product.selling_price }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{% trans "Profit Margin" %}:</td>
                                    <td>
                                        <span class="badge bg-{% if product.profit_margin > 20 %}success{% elif product.profit_margin > 10 %}warning{% else %}danger{% endif %}">
                                            {{ product.profit_margin|floatformat:2 }}%
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">{% trans "Created" %}:</td>
                                    <td>{{ product.created_at|date:"M d, Y" }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    {% if product.description %}
                        <div class="mt-3">
                            <h6>{% trans "Description" %}</h6>
                            <p class="text-muted">{{ product.description }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Stock Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-warehouse me-2"></i>{% trans "Stock Information" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h3 class="text-primary">{{ product.stock_quantity }}</h3>
                                <p class="text-muted mb-0">{% trans "Current Stock" %}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h3 class="text-warning">{{ product.min_stock_level }}</h3>
                                <p class="text-muted mb-0">{% trans "Minimum Level" %}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                {% if product.is_low_stock %}
                                    <h3 class="text-danger">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </h3>
                                    <p class="text-danger mb-0">{% trans "Low Stock" %}</p>
                                {% elif product.stock_quantity == 0 %}
                                    <h3 class="text-danger">
                                        <i class="fas fa-times-circle"></i>
                                    </h3>
                                    <p class="text-danger mb-0">{% trans "Out of Stock" %}</p>
                                {% else %}
                                    <h3 class="text-success">
                                        <i class="fas fa-check-circle"></i>
                                    </h3>
                                    <p class="text-success mb-0">{% trans "In Stock" %}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Product Image and Barcode -->
        <div class="col-lg-4">
            <!-- Product Image -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-image me-2"></i>{% trans "Product Image" %}
                    </h6>
                </div>
                <div class="card-body text-center">
                    {% if product.product_image %}
                        <img src="{{ product.product_image.url }}" alt="{{ product.name }}" 
                             class="img-fluid rounded" style="max-height: 200px;">
                    {% else %}
                        <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                             style="height: 200px;">
                            <i class="fas fa-box text-muted" style="font-size: 3rem;"></i>
                        </div>
                        <p class="text-muted mt-2">{% trans "No image available" %}</p>
                    {% endif %}
                </div>
            </div>
            
            <!-- Barcode -->
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-barcode me-2"></i>{% trans "Barcode" %}
                    </h6>
                    {% if not product.barcode_image %}
                        <a href="{% url 'products:generate_barcode' product.pk %}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-plus me-1"></i>{% trans "Generate" %}
                        </a>
                    {% endif %}
                </div>
                <div class="card-body text-center">
                    {% if product.barcode_image %}
                        <img src="{{ product.barcode_image.url }}" alt="Barcode for {{ product.sku }}" 
                             class="img-fluid">
                        <p class="text-muted mt-2 small">{{ product.sku }}</p>
                    {% else %}
                        <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                             style="height: 100px;">
                            <i class="fas fa-barcode text-muted" style="font-size: 2rem;"></i>
                        </div>
                        <p class="text-muted mt-2">{% trans "No barcode generated" %}</p>
                    {% endif %}
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>{% trans "Quick Actions" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'products:product_update' product.pk %}" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i>{% trans "Edit Product" %}
                        </a>
                        {% if not product.barcode_image %}
                            <a href="{% url 'products:generate_barcode' product.pk %}" class="btn btn-outline-info">
                                <i class="fas fa-barcode me-2"></i>{% trans "Generate Barcode" %}
                            </a>
                        {% endif %}
                        <a href="{% url 'products:product_delete' product.pk %}" 
                           class="btn btn-outline-danger delete-confirm"
                           data-item-name="{{ product.name }}">
                            <i class="fas fa-trash me-2"></i>{% trans "Delete Product" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
