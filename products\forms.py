from django import forms
from django.utils.translation import gettext_lazy as _
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Row, Column, Submit, HTML, Field
from crispy_forms.bootstrap import FormActions
from .models import Product, Category, Supplier


class CategoryForm(forms.ModelForm):
    """Form for creating and editing categories"""
    
    class Meta:
        model = Category
        fields = ['name', 'description', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Field('name', css_class='mb-3'),
            Field('description', css_class='mb-3'),
            Field('is_active', css_class='mb-3'),
            Submit('submit', _('Save Category'), css_class='btn btn-primary')
        )


class SupplierForm(forms.ModelForm):
    """Form for creating and editing suppliers"""
    
    class Meta:
        model = Supplier
        fields = [
            'name', 'contact_person', 'phone', 'email', 'website',
            'address', 'city', 'country', 'company_registration',
            'tax_id', 'notes', 'is_active'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'contact_person': forms.TextInput(attrs={'class': 'form-control'}),
            'phone': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'website': forms.URLInput(attrs={'class': 'form-control'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'city': forms.TextInput(attrs={'class': 'form-control'}),
            'country': forms.Select(attrs={'class': 'form-control'}),
            'company_registration': forms.TextInput(attrs={'class': 'form-control'}),
            'tax_id': forms.TextInput(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('name', css_class='form-group col-md-6 mb-3'),
                Column('contact_person', css_class='form-group col-md-6 mb-3'),
            ),
            Row(
                Column('phone', css_class='form-group col-md-6 mb-3'),
                Column('email', css_class='form-group col-md-6 mb-3'),
            ),
            Row(
                Column('website', css_class='form-group col-md-6 mb-3'),
                Column('company_registration', css_class='form-group col-md-6 mb-3'),
            ),
            Field('address', css_class='mb-3'),
            Row(
                Column('city', css_class='form-group col-md-6 mb-3'),
                Column('country', css_class='form-group col-md-6 mb-3'),
            ),
            Row(
                Column('tax_id', css_class='form-group col-md-6 mb-3'),
                Column('is_active', css_class='form-group col-md-6 mb-3'),
            ),
            Field('notes', css_class='mb-3'),
            Submit('submit', _('Save Supplier'), css_class='btn btn-primary')
        )


class ProductForm(forms.ModelForm):
    """Form for creating and editing products"""
    
    class Meta:
        model = Product
        fields = [
            'name', 'sku', 'category', 'description', 'cost_price', 'selling_price',
            'stock_quantity', 'min_stock_level', 'product_image', 'is_active'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'sku': forms.TextInput(attrs={'class': 'form-control'}),
            'category': forms.Select(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'cost_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'selling_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'stock_quantity': forms.NumberInput(attrs={'class': 'form-control'}),
            'min_stock_level': forms.NumberInput(attrs={'class': 'form-control'}),
            'product_image': forms.FileInput(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('name', css_class='form-group col-md-8 mb-3'),
                Column('sku', css_class='form-group col-md-4 mb-3'),
            ),
            Row(
                Column('category', css_class='form-group col-md-6 mb-3'),
                Column('is_active', css_class='form-group col-md-6 mb-3'),
            ),
            Field('description', css_class='mb-3'),
            Row(
                Column('cost_price', css_class='form-group col-md-6 mb-3'),
                Column('selling_price', css_class='form-group col-md-6 mb-3'),
            ),
            Row(
                Column('stock_quantity', css_class='form-group col-md-6 mb-3'),
                Column('min_stock_level', css_class='form-group col-md-6 mb-3'),
            ),
            Field('product_image', css_class='mb-3'),
            Submit('submit', _('Save Product'), css_class='btn btn-primary')
        )
    
    def clean_selling_price(self):
        cost_price = self.cleaned_data.get('cost_price')
        selling_price = self.cleaned_data.get('selling_price')
        
        if cost_price and selling_price and selling_price < cost_price:
            raise forms.ValidationError(_('Selling price cannot be less than cost price.'))
        
        return selling_price


class ProductSearchForm(forms.Form):
    """Form for searching and filtering products"""
    
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Search by name, SKU, or description')
        })
    )
    
    category = forms.ModelChoiceField(
        queryset=Category.objects.filter(is_active=True),
        required=False,
        empty_label=_('All Categories'),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    stock_status = forms.ChoiceField(
        choices=[
            ('', _('All Products')),
            ('in_stock', _('In Stock')),
            ('low_stock', _('Low Stock')),
            ('out_of_stock', _('Out of Stock')),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    is_active = forms.ChoiceField(
        choices=[
            ('', _('All Status')),
            ('true', _('Active')),
            ('false', _('Inactive')),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )


class BulkProductImportForm(forms.Form):
    """Form for bulk importing products from CSV/Excel"""
    
    file = forms.FileField(
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': '.csv,.xlsx,.xls'
        }),
        help_text=_('Upload CSV or Excel file with product data')
    )
    
    update_existing = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        help_text=_('Update existing products if SKU matches')
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Field('file', css_class='mb-3'),
            Field('update_existing', css_class='mb-3'),
            Submit('submit', _('Import Products'), css_class='btn btn-primary')
        )
    
    def clean_file(self):
        file = self.cleaned_data.get('file')
        if file:
            if not file.name.endswith(('.csv', '.xlsx', '.xls')):
                raise forms.ValidationError(_('Please upload a CSV or Excel file.'))
            
            # Check file size (max 5MB)
            if file.size > 5 * 1024 * 1024:
                raise forms.ValidationError(_('File size cannot exceed 5MB.'))
        
        return file
