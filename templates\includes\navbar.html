{% load i18n %}
{% load arabic_trans %}

<nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
    <div class="container-fluid">
        <!-- Brand -->
        <a class="navbar-brand d-flex align-items-center" href="{% url 'dashboard:home' %}">
            <i class="fas fa-receipt me-2"></i>
            <span class="fw-bold">{{ COMPANY_NAME }}</span>
        </a>
        
        <!-- Mobile Toggle -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <!-- Navigation Links -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                
                <!-- Dashboard -->
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.app_name == 'dashboard' %}active{% endif %}"
                       href="{% url 'dashboard:home' %}">
                        <i class="fas fa-tachometer-alt me-1"></i>
                        {% trans "Dashboard" %}
                    </a>
                </li>
                
                <!-- Products -->
                {% if user.role == 'admin' or user.role == 'sales_employee' %}
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle {% if request.resolver_match.app_name == 'products' %}active{% endif %}" 
                       href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-box me-1"></i>
                        {% trans "Products" %}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{% url 'products:product_list' %}"><i class="fas fa-list me-2"></i>{% trans "All Products" %}</a></li>
                        <li><a class="dropdown-item" href="{% url 'products:product_create' %}"><i class="fas fa-plus me-2"></i>{% trans "Add Product" %}</a></li>
                        <li><a class="dropdown-item" href="{% url 'products:category_list' %}"><i class="fas fa-tags me-2"></i>{% trans "Categories" %}</a></li>
                        <li><a class="dropdown-item" href="{% url 'products:supplier_list' %}"><i class="fas fa-truck me-2"></i>{% trans "Suppliers" %}</a></li>
                    </ul>
                </li>
                {% endif %}
                
                <!-- Customers -->
                {% if user.role == 'admin' or user.role == 'sales_employee' %}
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle {% if request.resolver_match.app_name == 'customers' %}active{% endif %}" 
                       href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-users me-1"></i>
                        {% trans "Customers" %}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{% url 'customers:customer_list' %}"><i class="fas fa-list me-2"></i>{% trans "All Customers" %}</a></li>
                        <li><a class="dropdown-item" href="{% url 'customers:customer_create' %}"><i class="fas fa-user-plus me-2"></i>{% trans "Add Customer" %}</a></li>
                    </ul>
                </li>
                {% endif %}
                
                <!-- Invoices -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle {% if request.resolver_match.app_name == 'invoices' %}active{% endif %}" 
                       href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-file-invoice me-1"></i>
                        {% trans "Invoices" %}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{% url 'invoices:invoice_list' %}"><i class="fas fa-list me-2"></i>{% trans "All Invoices" %}</a></li>
                        <li><a class="dropdown-item" href="{% url 'invoices:invoice_create' %}"><i class="fas fa-plus me-2"></i>{% trans "Create Invoice" %}</a></li>
                        <li><a class="dropdown-item" href="{% url 'invoices:quick_invoice' %}"><i class="fas fa-bolt me-2"></i>{% trans "Quick Invoice" %}</a></li>
                        {% if user.role == 'admin' or user.role == 'accountant' %}
                        <li><a class="dropdown-item" href="{% url 'invoices:overdue_invoices' %}"><i class="fas fa-exclamation-triangle me-2"></i>{% trans "Overdue Invoices" %}</a></li>
                        {% endif %}
                    </ul>
                </li>
                
                <!-- Reports -->
                {% if user.role == 'admin' or user.role == 'accountant' %}
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle {% if request.resolver_match.app_name == 'reports' %}active{% endif %}" 
                       href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-chart-bar me-1"></i>
                        {% trans "Reports" %}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{% url 'reports:sales_report' %}"><i class="fas fa-chart-line me-2"></i>{% trans "Sales Reports" %}</a></li>
                        <li><a class="dropdown-item" href="{% url 'reports:inventory_report' %}"><i class="fas fa-boxes me-2"></i>{% trans "Inventory Reports" %}</a></li>
                        <li><a class="dropdown-item" href="{% url 'reports:financial_report' %}"><i class="fas fa-dollar-sign me-2"></i>{% trans "Financial Reports" %}</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{% url 'reports:dashboard_analytics' %}"><i class="fas fa-tachometer-alt me-2"></i>{% trans "Dashboard Analytics" %}</a></li>
                    </ul>
                </li>
                {% endif %}
                
                <!-- Settings (Admin only) -->
                {% if user.role == 'admin' %}
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-cog me-1"></i>
                        {% trans "Settings" %}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{% url 'accounts:user_list' %}"><i class="fas fa-users-cog me-2"></i>{% trans "User Management" %}</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-building me-2"></i>{% trans "Company Settings" %}</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-percentage me-2"></i>{% trans "Tax Settings" %}</a></li>
                    </ul>
                </li>
                {% endif %}
            </ul>
            
            <!-- Language Switcher -->
            <ul class="navbar-nav me-3">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-globe me-1"></i>
                        {% if CURRENT_LANGUAGE == 'ar' %}العربية{% else %}English{% endif %}
                    </a>
                    <ul class="dropdown-menu">
                        <li>
                            <form action="{% url 'set_language' %}" method="post" class="d-inline">
                                {% csrf_token %}
                                <input name="next" type="hidden" value="{{ request.get_full_path }}" />
                                <input type="hidden" name="language" value="ar">
                                <button type="submit" class="dropdown-item {% if CURRENT_LANGUAGE == 'ar' %}active{% endif %}">
                                    <i class="fas fa-check me-2 {% if CURRENT_LANGUAGE != 'ar' %}invisible{% endif %}"></i>العربية
                                </button>
                            </form>
                        </li>
                        <li>
                            <form action="{% url 'set_language' %}" method="post" class="d-inline">
                                {% csrf_token %}
                                <input name="next" type="hidden" value="{{ request.get_full_path }}" />
                                <input type="hidden" name="language" value="en">
                                <button type="submit" class="dropdown-item {% if CURRENT_LANGUAGE == 'en' %}active{% endif %}">
                                    <i class="fas fa-check me-2 {% if CURRENT_LANGUAGE != 'en' %}invisible{% endif %}"></i>English
                                </button>
                            </form>
                        </li>
                    </ul>
                </li>
            </ul>

            <!-- User Menu -->
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                        {% if user.profile_image %}
                            <img src="{{ user.profile_image.url }}" alt="Profile" class="rounded-circle me-2" width="32" height="32">
                        {% else %}
                            <i class="fas fa-user-circle me-2 fs-4"></i>
                        {% endif %}
                        <span>{{ user.full_name|default:user.username }}</span>
                        <small class="text-light ms-1">({{ user.get_role_display }})</small>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="{% url 'accounts:profile' %}"><i class="fas fa-user me-2"></i>{% trans "Profile" %}</a></li>
                        <li><a class="dropdown-item" href="{% url 'accounts:change_password' %}"><i class="fas fa-key me-2"></i>{% trans "Change Password" %}</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="{% url 'accounts:logout' %}"><i class="fas fa-sign-out-alt me-2"></i>{% trans "Logout" %}</a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Spacer for fixed navbar -->
<div style="height: 76px;"></div>
