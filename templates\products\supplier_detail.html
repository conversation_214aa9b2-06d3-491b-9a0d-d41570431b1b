{% extends 'base.html' %}
{% load i18n %}

{% block title %}{{ supplier.name }} - {% trans "Supplier Details" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ supplier.name }}</h1>
            <p class="text-muted">{% trans "Supplier Details" %}</p>
        </div>
        <div class="btn-group" role="group">
            <a href="{% url 'products:supplier_update' supplier.pk %}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>{% trans "Edit Supplier" %}
            </a>
            <a href="{% url 'products:supplier_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Suppliers" %}
            </a>
        </div>
    </div>
    
    <div class="row">
        <!-- Supplier Information -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>{% trans "Basic Information" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">{% trans "Supplier Name" %}</label>
                                <p class="mb-0">{{ supplier.name }}</p>
                            </div>
                            {% if supplier.company_registration %}
                            <div class="mb-3">
                                <label class="form-label fw-bold">{% trans "Company Registration" %}</label>
                                <p class="mb-0">{{ supplier.company_registration }}</p>
                            </div>
                            {% endif %}
                            {% if supplier.tax_id %}
                            <div class="mb-3">
                                <label class="form-label fw-bold">{% trans "Tax ID" %}</label>
                                <p class="mb-0">{{ supplier.tax_id }}</p>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">{% trans "Status" %}</label>
                                <p class="mb-0">
                                    {% if supplier.is_active %}
                                        <span class="badge bg-success">{% trans "Active" %}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                    {% endif %}
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">{% trans "Created" %}</label>
                                <p class="mb-0">{{ supplier.created_at|date:"M d, Y" }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-address-book me-2"></i>{% trans "Contact Information" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            {% if supplier.contact_person %}
                            <div class="mb-3">
                                <label class="form-label fw-bold">{% trans "Contact Person" %}</label>
                                <p class="mb-0">{{ supplier.contact_person }}</p>
                            </div>
                            {% endif %}
                            <div class="mb-3">
                                <label class="form-label fw-bold">{% trans "Phone" %}</label>
                                <p class="mb-0">
                                    <a href="tel:{{ supplier.phone }}">{{ supplier.phone }}</a>
                                </p>
                            </div>
                            {% if supplier.email %}
                            <div class="mb-3">
                                <label class="form-label fw-bold">{% trans "Email" %}</label>
                                <p class="mb-0">
                                    <a href="mailto:{{ supplier.email }}">{{ supplier.email }}</a>
                                </p>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            {% if supplier.website %}
                            <div class="mb-3">
                                <label class="form-label fw-bold">{% trans "Website" %}</label>
                                <p class="mb-0">
                                    <a href="{{ supplier.website }}" target="_blank">{{ supplier.website }}</a>
                                </p>
                            </div>
                            {% endif %}
                            {% if supplier.address %}
                            <div class="mb-3">
                                <label class="form-label fw-bold">{% trans "Address" %}</label>
                                <p class="mb-0">{{ supplier.address }}</p>
                            </div>
                            {% endif %}
                            <div class="mb-3">
                                <label class="form-label fw-bold">{% trans "Location" %}</label>
                                <p class="mb-0">
                                    {% if supplier.city %}{{ supplier.city }}, {% endif %}{{ supplier.country }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Products -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-box me-2"></i>{% trans "Products" %}
                        <span class="badge bg-primary ms-2">{{ supplier.products.count }}</span>
                    </h6>
                </div>
                <div class="card-body">
                    {% if supplier.products.exists %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{% trans "Product" %}</th>
                                        <th>{% trans "SKU" %}</th>
                                        <th>{% trans "Category" %}</th>
                                        <th class="text-end">{% trans "Cost Price" %}</th>
                                        <th class="text-end">{% trans "Stock" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for product in supplier.products.all %}
                                        <tr>
                                            <td>
                                                <a href="{% url 'products:product_detail' product.pk %}">
                                                    {{ product.name }}
                                                </a>
                                            </td>
                                            <td><code>{{ product.sku }}</code></td>
                                            <td>
                                                {% if product.category %}
                                                    <span class="badge bg-info">{{ product.category.name }}</span>
                                                {% else %}
                                                    <span class="text-muted">{% trans "No Category" %}</span>
                                                {% endif %}
                                            </td>
                                            <td class="text-end">${{ product.cost_price }}</td>
                                            <td class="text-end">
                                                <span class="{% if product.stock_quantity <= product.min_stock_level %}text-danger{% elif product.stock_quantity <= product.min_stock_level|add:10 %}text-warning{% else %}text-success{% endif %}">
                                                    {{ product.stock_quantity }}
                                                </span>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-box text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2">{% trans "No products from this supplier yet." %}</p>
                            <a href="{% url 'products:product_create' %}?supplier={{ supplier.pk }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-plus me-1"></i>{% trans "Add Product" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Stats -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>{% trans "Quick Stats" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h4 class="text-primary">{{ supplier.products.count }}</h4>
                            <small class="text-muted">{% trans "Products" %}</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-info">{{ supplier.products.filter.is_active=True.count }}</h4>
                            <small class="text-muted">{% trans "Active Products" %}</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">${{ total_value|default:"0.00" }}</h4>
                            <small class="text-muted">{% trans "Total Inventory Value" %}</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning">{{ low_stock_count|default:"0" }}</h4>
                            <small class="text-muted">{% trans "Low Stock Items" %}</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>{% trans "Quick Actions" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'products:supplier_update' supplier.pk %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit me-2"></i>{% trans "Edit Supplier" %}
                        </a>
                        <a href="{% url 'products:product_create' %}?supplier={{ supplier.pk }}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-plus me-2"></i>{% trans "Add Product" %}
                        </a>
                        {% if supplier.email %}
                        <a href="mailto:{{ supplier.email }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-envelope me-2"></i>{% trans "Send Email" %}
                        </a>
                        {% endif %}
                        {% if supplier.phone %}
                        <a href="tel:{{ supplier.phone }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-phone me-2"></i>{% trans "Call Supplier" %}
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Notes -->
            {% if supplier.notes %}
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-sticky-note me-2"></i>{% trans "Notes" %}
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-0">{{ supplier.notes|linebreaks }}</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
