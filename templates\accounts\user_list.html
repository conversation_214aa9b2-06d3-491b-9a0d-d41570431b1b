{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "User Management" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{% trans "User Management" %}</h1>
            <p class="text-muted">{% trans "Manage system users and their roles" %}</p>
        </div>
        <a href="{% url 'accounts:user_create' %}" class="btn btn-primary">
            <i class="fas fa-user-plus me-2"></i>{% trans "Add New User" %}
        </a>
    </div>
    
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">{% trans "Search" %}</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ search }}" placeholder="{% trans 'Search by name, username, or email' %}">
                </div>
                <div class="col-md-3">
                    <label for="role" class="form-label">{% trans "Role" %}</label>
                    <select class="form-select" id="role" name="role">
                        <option value="">{% trans "All Roles" %}</option>
                        {% for value, label in roles %}
                            <option value="{{ value }}" {% if value == selected_role %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search me-1"></i>{% trans "Filter" %}
                    </button>
                    <a href="{% url 'accounts:user_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Users Table -->
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-users me-2"></i>{% trans "System Users" %}
                <span class="badge bg-primary ms-2">{{ page_obj.paginator.count }}</span>
            </h6>
        </div>
        <div class="card-body p-0">
            {% if users %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>{% trans "User" %}</th>
                                <th>{% trans "Role" %}</th>
                                <th>{% trans "Contact" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Joined" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if user.profile_image %}
                                                <img src="{{ user.profile_image.url }}" alt="Profile" 
                                                     class="rounded-circle me-3" width="40" height="40">
                                            {% else %}
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" 
                                                     style="width: 40px; height: 40px;">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                            {% endif %}
                                            <div>
                                                <div class="fw-bold">{{ user.full_name|default:user.username }}</div>
                                                <small class="text-muted">@{{ user.username }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if user.role == 'admin' %}danger{% elif user.role == 'accountant' %}warning{% else %}info{% endif %}">
                                            {{ user.get_role_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div>{{ user.email }}</div>
                                        {% if user.phone %}
                                            <small class="text-muted">{{ user.phone }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if user.is_active %}success{% else %}danger{% endif %}">
                                            {% if user.is_active %}{% trans "Active" %}{% else %}{% trans "Inactive" %}{% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        <div>{{ user.created_at|date:"M d, Y" }}</div>
                                        <small class="text-muted">{{ user.created_at|timesince }} {% trans "ago" %}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{% url 'accounts:user_update' user.pk %}" 
                                               class="btn btn-outline-primary" title="{% trans 'Edit User' %}">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if user != request.user %}
                                                <a href="{% url 'accounts:user_toggle_status' user.pk %}" 
                                                   class="btn btn-outline-{% if user.is_active %}warning{% else %}success{% endif %} toggle-user-status"
                                                   title="{% if user.is_active %}{% trans 'Deactivate User' %}{% else %}{% trans 'Activate User' %}{% endif %}">
                                                    <i class="fas fa-{% if user.is_active %}ban{% else %}check{% endif %}"></i>
                                                </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if is_paginated %}
                    <div class="card-footer">
                        <nav aria-label="{% trans 'Users pagination' %}">
                            <ul class="pagination justify-content-center mb-0">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% if search %}&search={{ search }}{% endif %}{% if selected_role %}&role={{ selected_role }}{% endif %}">
                                            {% trans "First" %}
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if selected_role %}&role={{ selected_role }}{% endif %}">
                                            {% trans "Previous" %}
                                        </a>
                                    </li>
                                {% endif %}
                                
                                <li class="page-item active">
                                    <span class="page-link">
                                        {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>
                                
                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if selected_role %}&role={{ selected_role }}{% endif %}">
                                            {% trans "Next" %}
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}{% if selected_role %}&role={{ selected_role }}{% endif %}">
                                            {% trans "Last" %}
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users text-muted" style="font-size: 3rem;"></i>
                    <h5 class="mt-3 text-muted">{% trans "No users found" %}</h5>
                    <p class="text-muted">{% trans "Try adjusting your search criteria or add a new user." %}</p>
                    <a href="{% url 'accounts:user_create' %}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>{% trans "Add First User" %}
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
