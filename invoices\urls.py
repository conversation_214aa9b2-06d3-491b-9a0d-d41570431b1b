from django.urls import path
from . import views

app_name = 'invoices'

urlpatterns = [
    # Invoice URLs
    path('', views.invoice_list, name='invoice_list'),
    path('create/', views.invoice_create, name='invoice_create'),
    path('<int:pk>/', views.invoice_detail, name='invoice_detail'),
    path('<int:pk>/edit/', views.invoice_update, name='invoice_update'),
    path('<int:pk>/delete/', views.invoice_delete, name='invoice_delete'),
    path('<int:pk>/pdf/', views.invoice_pdf, name='invoice_pdf'),
    path('<int:pk>/update-status/', views.update_invoice_status, name='update_invoice_status'),

    # Payment URLs
    path('<int:pk>/record-payment/', views.record_payment, name='record_payment'),

    # Quick invoice (POS style)
    path('quick/', views.quick_invoice, name='quick_invoice'),

    # AJAX URLs
    path('api/product/<int:product_id>/', views.get_product_info, name='get_product_info'),

    # Special views
    path('overdue/', views.overdue_invoices, name='overdue_invoices'),
]
