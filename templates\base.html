{% load i18n %}
<!DOCTYPE html>
<html lang="{% if CURRENT_LANGUAGE == 'ar' %}ar{% else %}en{% endif %}" dir="{% if IS_RTL %}rtl{% else %}ltr{% endif %}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    
    <title>{% block title %}{{ COMPANY_NAME }} - Sales Invoice Management System{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    {% if IS_RTL %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    {% if IS_RTL %}
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    {% else %}
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    {% endif %}
    
    <!-- Custom CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    
    {% block extra_css %}{% endblock %}
</head>
<body class="{% if IS_RTL %}rtl-layout{% else %}ltr-layout{% endif %}">
    
    <!-- Navigation -->
    {% if user.is_authenticated %}
        {% include 'includes/navbar.html' %}
    {% endif %}
    
    <!-- Main Content -->
    <div class="{% if user.is_authenticated %}main-content{% else %}auth-content{% endif %}">
        
        <!-- Messages -->
        {% if messages %}
            <div class="container-fluid mt-3">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                        <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'warning' %}exclamation-circle{% else %}info-circle{% endif %} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
        
        <!-- Page Content -->
        {% block content %}{% endblock %}
    </div>
    
    <!-- Footer -->
    {% if user.is_authenticated %}
        {% include 'includes/footer.html' %}
    {% endif %}
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    
    {% block extra_js %}{% endblock %}
    
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay d-none">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
</body>
</html>
