from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Sum, Count, F
from django.utils import timezone
from datetime import datetime, timedelta
from invoices.models import Invoice
from customers.models import Customer
from products.models import Product


@login_required
def dashboard_home(request):
    """Dashboard home view with statistics"""

    # Get current date
    today = timezone.now().date()
    current_month = today.replace(day=1)

    # Calculate statistics
    stats = {
        'today_sales': Invoice.objects.filter(
            invoice_date=today,
            status='paid'
        ).aggregate(total=Sum('total_amount'))['total'] or 0,

        'monthly_invoices': Invoice.objects.filter(
            invoice_date__gte=current_month
        ).count(),

        'total_customers': Customer.objects.filter(is_active=True).count(),

        'low_stock_items': Product.objects.filter(
            stock_quantity__lte=F('min_stock_level'),
            is_active=True
        ).count(),
    }

    # Get recent invoices
    recent_invoices = Invoice.objects.select_related('customer', 'created_by').order_by('-created_at')[:5]

    # Get low stock products
    low_stock_products = Product.objects.filter(
        stock_quantity__lte=F('min_stock_level'),
        is_active=True
    ).order_by('stock_quantity')[:5]

    context = {
        'stats': stats,
        'recent_invoices': recent_invoices,
        'low_stock_products': low_stock_products,
    }

    return render(request, 'dashboard/home.html', context)
