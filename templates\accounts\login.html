{% extends 'base.html' %}
{% load i18n crispy_forms_tags %}

{% block title %}{% trans "Login" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card auth-card">
                <div class="card-body p-5">
                    <!-- Logo -->
                    <div class="text-center mb-4">
                        <i class="fas fa-receipt auth-logo"></i>
                        <h3 class="fw-bold text-primary">{{ COMPANY_NAME }}</h3>
                        <p class="text-muted">{% trans "Sales Invoice Management System" %}</p>
                    </div>
                    
                    <!-- Login Form -->
                    <form method="post" class="fade-in">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">
                                <i class="fas fa-user me-2"></i>{% trans "Username" %}
                            </label>
                            {{ form.username }}
                            {% if form.username.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.username.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.password.id_for_label }}" class="form-label">
                                <i class="fas fa-lock me-2"></i>{% trans "Password" %}
                            </label>
                            {{ form.password }}
                            {% if form.password.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.password.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3 form-check">
                            {{ form.remember_me }}
                            <label class="form-check-label" for="{{ form.remember_me.id_for_label }}">
                                {% trans "Remember me" %}
                            </label>
                        </div>
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors.0 }}
                            </div>
                        {% endif %}
                        
                        <button type="submit" class="btn btn-primary w-100 py-2 fw-bold">
                            <i class="fas fa-sign-in-alt me-2"></i>{% trans "Login" %}
                        </button>
                    </form>
                    
                    <!-- Footer -->
                    <div class="text-center mt-4">
                        <small class="text-muted">
                            {% trans "Secure login powered by Django" %}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .auth-content {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        background-attachment: fixed;
    }
    
    .auth-card {
        animation: slideIn 0.5s ease-out;
    }
    
    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}
