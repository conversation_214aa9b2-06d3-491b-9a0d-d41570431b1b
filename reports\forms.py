from django import forms
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Row, Column, Submit, Field, HTML
from datetime import datetime, timedelta
from customers.models import Customer
from products.models import Product, Category
from accounts.models import User


class DateRangeForm(forms.Form):
    """Base form for date range selection"""
    
    PERIOD_CHOICES = [
        ('today', _('Today')),
        ('yesterday', _('Yesterday')),
        ('this_week', _('This Week')),
        ('last_week', _('Last Week')),
        ('this_month', _('This Month')),
        ('last_month', _('Last Month')),
        ('this_quarter', _('This Quarter')),
        ('last_quarter', _('Last Quarter')),
        ('this_year', _('This Year')),
        ('last_year', _('Last Year')),
        ('custom', _('Custom Range')),
    ]
    
    period = forms.ChoiceField(
        choices=PERIOD_CHOICES,
        initial='this_month',
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Period')
    )
    
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        label=_('Start Date')
    )
    
    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        label=_('End Date')
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('period', css_class='form-group col-md-4 mb-3'),
                Column('start_date', css_class='form-group col-md-4 mb-3'),
                Column('end_date', css_class='form-group col-md-4 mb-3'),
            ),
            Submit('submit', _('Generate Report'), css_class='btn btn-primary')
        )
    
    def clean(self):
        cleaned_data = super().clean()
        period = cleaned_data.get('period')
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        
        if period == 'custom':
            if not start_date or not end_date:
                raise forms.ValidationError(_('Start date and end date are required for custom range.'))
            if start_date > end_date:
                raise forms.ValidationError(_('Start date cannot be after end date.'))
        
        return cleaned_data
    
    def get_date_range(self):
        """Get the actual date range based on selected period"""
        period = self.cleaned_data.get('period')
        today = timezone.now().date()
        
        if period == 'today':
            return today, today
        elif period == 'yesterday':
            yesterday = today - timedelta(days=1)
            return yesterday, yesterday
        elif period == 'this_week':
            start = today - timedelta(days=today.weekday())
            return start, today
        elif period == 'last_week':
            start = today - timedelta(days=today.weekday() + 7)
            end = start + timedelta(days=6)
            return start, end
        elif period == 'this_month':
            start = today.replace(day=1)
            return start, today
        elif period == 'last_month':
            first_this_month = today.replace(day=1)
            last_month_end = first_this_month - timedelta(days=1)
            last_month_start = last_month_end.replace(day=1)
            return last_month_start, last_month_end
        elif period == 'this_quarter':
            quarter = (today.month - 1) // 3 + 1
            start = today.replace(month=(quarter - 1) * 3 + 1, day=1)
            return start, today
        elif period == 'last_quarter':
            quarter = (today.month - 1) // 3 + 1
            if quarter == 1:
                start = today.replace(year=today.year - 1, month=10, day=1)
                end = today.replace(year=today.year - 1, month=12, day=31)
            else:
                start = today.replace(month=(quarter - 2) * 3 + 1, day=1)
                end_month = (quarter - 1) * 3
                end = today.replace(month=end_month, day=1) - timedelta(days=1)
            return start, end
        elif period == 'this_year':
            start = today.replace(month=1, day=1)
            return start, today
        elif period == 'last_year':
            start = today.replace(year=today.year - 1, month=1, day=1)
            end = today.replace(year=today.year - 1, month=12, day=31)
            return start, end
        elif period == 'custom':
            return self.cleaned_data.get('start_date'), self.cleaned_data.get('end_date')
        
        return today, today


class SalesReportForm(DateRangeForm):
    """Form for sales reports"""
    
    customer = forms.ModelChoiceField(
        queryset=Customer.objects.filter(is_active=True),
        required=False,
        empty_label=_('All Customers'),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Customer')
    )
    
    sales_person = forms.ModelChoiceField(
        queryset=User.objects.filter(role__in=[User.UserRole.ADMIN, User.UserRole.SALES_EMPLOYEE]),
        required=False,
        empty_label=_('All Sales People'),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Sales Person')
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper.layout = Layout(
            Row(
                Column('period', css_class='form-group col-md-3 mb-3'),
                Column('start_date', css_class='form-group col-md-3 mb-3'),
                Column('end_date', css_class='form-group col-md-3 mb-3'),
                Column('customer', css_class='form-group col-md-3 mb-3'),
            ),
            Row(
                Column('sales_person', css_class='form-group col-md-6 mb-3'),
            ),
            Submit('submit', _('Generate Sales Report'), css_class='btn btn-primary')
        )


class InventoryReportForm(forms.Form):
    """Form for inventory reports"""
    
    REPORT_TYPE_CHOICES = [
        ('current_stock', _('Current Stock Levels')),
        ('low_stock', _('Low Stock Items')),
        ('out_of_stock', _('Out of Stock Items')),
        ('stock_movement', _('Stock Movement')),
        ('valuation', _('Inventory Valuation')),
    ]
    
    report_type = forms.ChoiceField(
        choices=REPORT_TYPE_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Report Type')
    )
    
    category = forms.ModelChoiceField(
        queryset=Category.objects.filter(is_active=True),
        required=False,
        empty_label=_('All Categories'),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Category')
    )
    
    include_inactive = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        label=_('Include Inactive Products')
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('report_type', css_class='form-group col-md-4 mb-3'),
                Column('category', css_class='form-group col-md-4 mb-3'),
                Column('include_inactive', css_class='form-group col-md-4 mb-3'),
            ),
            Submit('submit', _('Generate Inventory Report'), css_class='btn btn-primary')
        )


class FinancialReportForm(DateRangeForm):
    """Form for financial reports"""
    
    REPORT_TYPE_CHOICES = [
        ('profit_loss', _('Profit & Loss Statement')),
        ('revenue_summary', _('Revenue Summary')),
        ('payment_summary', _('Payment Summary')),
        ('outstanding_invoices', _('Outstanding Invoices')),
        ('customer_balance', _('Customer Balance Sheet')),
    ]
    
    report_type = forms.ChoiceField(
        choices=REPORT_TYPE_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label=_('Report Type')
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper.layout = Layout(
            Row(
                Column('report_type', css_class='form-group col-md-4 mb-3'),
                Column('period', css_class='form-group col-md-4 mb-3'),
                Column('start_date', css_class='form-group col-md-2 mb-3'),
                Column('end_date', css_class='form-group col-md-2 mb-3'),
            ),
            Submit('submit', _('Generate Financial Report'), css_class='btn btn-primary')
        )


class DashboardFilterForm(forms.Form):
    """Form for dashboard filters"""
    
    DASHBOARD_PERIOD_CHOICES = [
        ('7', _('Last 7 Days')),
        ('30', _('Last 30 Days')),
        ('90', _('Last 90 Days')),
        ('365', _('Last Year')),
    ]
    
    period = forms.ChoiceField(
        choices=DASHBOARD_PERIOD_CHOICES,
        initial='30',
        widget=forms.Select(attrs={'class': 'form-control form-control-sm'}),
        label=_('Period')
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'get'
        self.helper.layout = Layout(
            Row(
                Column('period', css_class='form-group col-md-12'),
            ),
        )
