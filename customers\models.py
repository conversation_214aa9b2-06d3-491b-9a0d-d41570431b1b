from django.db import models
from django.core.validators import MinValueValidator
from django.utils.translation import gettext_lazy as _


class Customer(models.Model):
    """Customer Model for CRM functionality"""

    class CustomerType(models.TextChoices):
        INDIVIDUAL = 'individual', _('Individual')
        BUSINESS = 'business', _('Business')

    customer_type = models.CharField(
        max_length=20,
        choices=CustomerType.choices,
        default=CustomerType.INDIVIDUAL,
        verbose_name=_('Customer Type')
    )

    name = models.CharField(
        max_length=200,
        verbose_name=_('Customer Name')
    )

    company_name = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name=_('Company Name')
    )

    phone = models.CharField(
        max_length=20,
        verbose_name=_('Phone Number')
    )

    email = models.EmailField(
        blank=True,
        null=True,
        verbose_name=_('Email Address')
    )

    address = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Address')
    )

    tax_id = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Tax ID')
    )

    credit_limit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name=_('Credit Limit')
    )

    payment_terms = models.PositiveIntegerField(
        default=30,
        help_text=_('Payment terms in days'),
        verbose_name=_('Payment Terms (Days)')
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Active Status')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )

    class Meta:
        verbose_name = _('Customer')
        verbose_name_plural = _('Customers')
        ordering = ['name']

    def __str__(self):
        return self.name

    @property
    def total_purchases(self):
        """Calculate total purchase amount"""
        from invoices.models import Invoice
        return Invoice.objects.filter(
            customer=self,
            status='paid'
        ).aggregate(
            total=models.Sum('total_amount')
        )['total'] or 0

    @property
    def outstanding_balance(self):
        """Calculate outstanding balance"""
        from invoices.models import Invoice
        return Invoice.objects.filter(
            customer=self,
            status__in=['pending', 'overdue']
        ).aggregate(
            total=models.Sum('total_amount')
        )['total'] or 0

    @property
    def available_credit(self):
        """Calculate available credit"""
        return self.credit_limit - self.outstanding_balance
