{% extends 'base.html' %}
{% load i18n crispy_forms_tags static %}

{% block title %}{% trans "Profile" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">{% trans "My Profile" %}</h1>
                    <p class="text-muted">{% trans "Manage your account information" %}</p>
                </div>
                <a href="{% url 'accounts:change_password' %}" class="btn btn-outline-primary">
                    <i class="fas fa-key me-2"></i>{% trans "Change Password" %}
                </a>
            </div>
            
            <div class="row">
                <!-- Profile Information -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            {% if user.profile_image %}
                                <img src="{{ user.profile_image.url }}" alt="Profile" class="rounded-circle mb-3" width="120" height="120">
                            {% else %}
                                <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 120px; height: 120px;">
                                    <i class="fas fa-user text-white" style="font-size: 3rem;"></i>
                                </div>
                            {% endif %}
                            
                            <h4 class="mb-1">{{ user.full_name|default:user.username }}</h4>
                            <p class="text-muted mb-2">{{ user.get_role_display }}</p>
                            <p class="text-muted small">
                                <i class="fas fa-envelope me-1"></i>{{ user.email }}
                            </p>
                            {% if user.phone %}
                                <p class="text-muted small">
                                    <i class="fas fa-phone me-1"></i>{{ user.phone }}
                                </p>
                            {% endif %}
                            
                            <div class="mt-3">
                                <span class="badge bg-{% if user.is_active %}success{% else %}danger{% endif %}">
                                    {% if user.is_active %}{% trans "Active" %}{% else %}{% trans "Inactive" %}{% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Account Statistics -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">{% trans "Account Information" %}</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h5 class="mb-1">{{ user.date_joined|date:"M Y" }}</h5>
                                        <small class="text-muted">{% trans "Member Since" %}</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h5 class="mb-1">{{ user.last_login|date:"M d" }}</h5>
                                    <small class="text-muted">{% trans "Last Login" %}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Profile Form -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">{% trans "Edit Profile" %}</h6>
                        </div>
                        <div class="card-body">
                            <form method="post" enctype="multipart/form-data">
                                {% csrf_token %}
                                {% crispy form %}
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .profile-image {
        object-fit: cover;
        border: 3px solid #fff;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
</style>
{% endblock %}
