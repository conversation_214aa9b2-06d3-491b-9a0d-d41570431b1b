{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Delete Product" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <!-- Page Header -->
            <div class="text-center mb-4">
                <h1 class="h3 mb-0 text-danger">{% trans "Delete Product" %}</h1>
                <p class="text-muted">{% trans "This action cannot be undone" %}</p>
            </div>
            
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>{% trans "Confirm Deletion" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        {% if object.product_image %}
                            <img src="{{ object.product_image.url }}" alt="{{ object.name }}" 
                                 class="rounded" width="100" height="100" style="object-fit: cover;">
                        {% else %}
                            <div class="bg-light rounded d-inline-flex align-items-center justify-content-center" 
                                 style="width: 100px; height: 100px;">
                                <i class="fas fa-box text-muted" style="font-size: 2rem;"></i>
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">{% trans "Are you sure you want to delete this product?" %}</h6>
                        <p class="mb-0">
                            {% trans "You are about to permanently delete" %} <strong>{{ object.name }}</strong> 
                            (SKU: <code>{{ object.sku }}</code>).
                        </p>
                    </div>
                    
                    <!-- Product Summary -->
                    <div class="row mb-4">
                        <div class="col-6">
                            <div class="text-center">
                                <h5 class="text-primary">${{ object.selling_price }}</h5>
                                <small class="text-muted">{% trans "Selling Price" %}</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h5 class="text-warning">{{ object.stock_quantity }}</h5>
                                <small class="text-muted">{% trans "Current Stock" %}</small>
                            </div>
                        </div>
                    </div>
                    
                    {% if object.stock_quantity > 0 %}
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {% trans "Warning: This product still has stock. Deleting it will remove all inventory records." %}
                        </div>
                    {% endif %}
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <a href="{% url 'products:product_detail' object.pk %}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times me-2"></i>{% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>{% trans "Yes, Delete Product" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Back Link -->
            <div class="text-center mt-4">
                <a href="{% url 'products:product_list' %}" class="text-muted">
                    <i class="fas fa-arrow-left me-1"></i>{% trans "Back to Products" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
