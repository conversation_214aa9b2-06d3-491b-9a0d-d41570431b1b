{% extends 'base.html' %}
{% load i18n crispy_forms_tags %}

{% block title %}{% trans "Create User" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">{% trans "Create New User" %}</h1>
                    <p class="text-muted">{% trans "Add a new user to the system" %}</p>
                </div>
                <a href="{% url 'accounts:user_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Users" %}
                </a>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>{% trans "User Information" %}
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        {% crispy form %}
                    </form>
                </div>
            </div>
            
            <!-- Role Permissions Info -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>{% trans "Role Permissions" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="border rounded p-3 h-100">
                                <h6 class="text-danger">
                                    <i class="fas fa-crown me-2"></i>{% trans "Admin" %}
                                </h6>
                                <ul class="list-unstyled small mb-0">
                                    <li><i class="fas fa-check text-success me-1"></i> {% trans "Full system access" %}</li>
                                    <li><i class="fas fa-check text-success me-1"></i> {% trans "User management" %}</li>
                                    <li><i class="fas fa-check text-success me-1"></i> {% trans "System settings" %}</li>
                                    <li><i class="fas fa-check text-success me-1"></i> {% trans "All reports" %}</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3 h-100">
                                <h6 class="text-info">
                                    <i class="fas fa-shopping-cart me-2"></i>{% trans "Sales Employee" %}
                                </h6>
                                <ul class="list-unstyled small mb-0">
                                    <li><i class="fas fa-check text-success me-1"></i> {% trans "Create invoices" %}</li>
                                    <li><i class="fas fa-check text-success me-1"></i> {% trans "Manage products" %}</li>
                                    <li><i class="fas fa-check text-success me-1"></i> {% trans "Manage customers" %}</li>
                                    <li><i class="fas fa-times text-danger me-1"></i> {% trans "Limited reports" %}</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3 h-100">
                                <h6 class="text-warning">
                                    <i class="fas fa-calculator me-2"></i>{% trans "Accountant" %}
                                </h6>
                                <ul class="list-unstyled small mb-0">
                                    <li><i class="fas fa-check text-success me-1"></i> {% trans "View all data" %}</li>
                                    <li><i class="fas fa-check text-success me-1"></i> {% trans "Financial reports" %}</li>
                                    <li><i class="fas fa-check text-success me-1"></i> {% trans "Payment management" %}</li>
                                    <li><i class="fas fa-times text-danger me-1"></i> {% trans "No user management" %}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
