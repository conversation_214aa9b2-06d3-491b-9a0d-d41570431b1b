{% extends 'base.html' %}
{% load i18n crispy_forms_tags %}

{% block title %}{% trans "Create Invoice" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{% trans "Create New Invoice" %}</h1>
            <p class="text-muted">{% trans "Generate a new sales invoice" %}</p>
        </div>
        <a href="{% url 'invoices:invoice_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Invoices" %}
        </a>
    </div>
    
    <form method="post" id="invoice-form">
        {% csrf_token %}
        
        <div class="row">
            <!-- Invoice Details -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-file-invoice me-2"></i>{% trans "Invoice Details" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        {% crispy form %}
                    </div>
                </div>
                
                <!-- Invoice Items -->
                <div class="card mt-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-list me-2"></i>{% trans "Invoice Items" %}
                        </h6>
                        <button type="button" class="btn btn-sm btn-outline-primary" id="add-item">
                            <i class="fas fa-plus me-1"></i>{% trans "Add Item" %}
                        </button>
                    </div>
                    <div class="card-body">
                        {{ formset.management_form }}
                        <div id="invoice-items">
                            {% for form in formset %}
                                <div class="invoice-item border rounded p-3 mb-3">
                                    <div class="row">
                                        <div class="col-md-4">
                                            {{ form.product.label_tag }}
                                            {{ form.product }}
                                            {{ form.product.errors }}
                                        </div>
                                        <div class="col-md-2">
                                            {{ form.quantity.label_tag }}
                                            {{ form.quantity }}
                                            {{ form.quantity.errors }}
                                        </div>
                                        <div class="col-md-2">
                                            {{ form.unit_price.label_tag }}
                                            {{ form.unit_price }}
                                            {{ form.unit_price.errors }}
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">{% trans "Line Total" %}</label>
                                            <input type="text" class="form-control line-total" readonly>
                                        </div>
                                        <div class="col-md-2 d-flex align-items-end">
                                            {% if forloop.counter0 > 0 %}
                                                <button type="button" class="btn btn-outline-danger btn-sm remove-item">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            {% endif %}
                                            {{ form.DELETE }}
                                        </div>
                                    </div>
                                    {{ form.id }}
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Invoice Summary -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-calculator me-2"></i>{% trans "Invoice Summary" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>{% trans "Subtotal" %}:</span>
                            <span id="subtotal">$0.00</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>{% trans "Discount" %}:</span>
                            <span id="discount">$0.00</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>{% trans "Tax" %}:</span>
                            <span id="tax">$0.00</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between fw-bold">
                            <span>{% trans "Total" %}:</span>
                            <span id="total">$0.00</span>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Product Add -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-search me-2"></i>{% trans "Quick Add Product" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <input type="text" class="form-control" id="product-search" 
                                   placeholder="{% trans 'Search products...' %}">
                        </div>
                        <div id="product-suggestions" class="list-group"></div>
                    </div>
                </div>
                
                <!-- Actions -->
                <div class="card mt-4">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" name="action" value="save_draft" class="btn btn-outline-secondary">
                                <i class="fas fa-save me-2"></i>{% trans "Save as Draft" %}
                            </button>
                            <button type="submit" name="action" value="save_pending" class="btn btn-primary">
                                <i class="fas fa-check me-2"></i>{% trans "Create Invoice" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    let formIndex = {{ formset.total_form_count }};
    const products = {{ products|safe }};
    
    // Calculate totals
    function calculateTotals() {
        let subtotal = 0;
        
        $('.invoice-item').each(function() {
            const quantity = parseFloat($(this).find('.quantity-input').val()) || 0;
            const price = parseFloat($(this).find('.price-input').val()) || 0;
            const lineTotal = quantity * price;
            
            $(this).find('.line-total').val(lineTotal.toFixed(2));
            subtotal += lineTotal;
        });
        
        const discountPercentage = parseFloat($('#id_discount_percentage').val()) || 0;
        const taxRate = parseFloat($('#id_tax_rate').val()) || 0;
        
        const discountAmount = (subtotal * discountPercentage) / 100;
        const taxableAmount = subtotal - discountAmount;
        const taxAmount = (taxableAmount * taxRate) / 100;
        const total = taxableAmount + taxAmount;
        
        $('#subtotal').text('$' + subtotal.toFixed(2));
        $('#discount').text('$' + discountAmount.toFixed(2));
        $('#tax').text('$' + taxAmount.toFixed(2));
        $('#total').text('$' + total.toFixed(2));
    }
    
    // Update product price when product is selected
    $(document).on('change', '.product-select', function() {
        const productId = $(this).val();
        const priceInput = $(this).closest('.invoice-item').find('.price-input');
        
        if (productId) {
            const product = products.find(p => p.id == productId);
            if (product) {
                priceInput.val(product.selling_price);
                calculateTotals();
            }
        }
    });
    
    // Calculate totals when inputs change
    $(document).on('input', '.quantity-input, .price-input, #id_discount_percentage, #id_tax_rate', calculateTotals);
    
    // Add new item
    $('#add-item').click(function() {
        const newForm = $('.invoice-item').first().clone();
        newForm.find('input, select').each(function() {
            const name = $(this).attr('name');
            if (name) {
                $(this).attr('name', name.replace(/\d+/, formIndex));
                $(this).attr('id', 'id_' + name.replace(/\d+/, formIndex));
            }
            $(this).val('');
        });
        
        newForm.find('.remove-item').show();
        newForm.find('input[type="checkbox"]').prop('checked', false);
        $('#invoice-items').append(newForm);
        
        formIndex++;
        $('#id_form-TOTAL_FORMS').val(formIndex);
        calculateTotals();
    });
    
    // Remove item
    $(document).on('click', '.remove-item', function() {
        const item = $(this).closest('.invoice-item');
        item.find('input[name$="-DELETE"]').prop('checked', true);
        item.hide();
        calculateTotals();
    });
    
    // Product search
    $('#product-search').on('input', function() {
        const query = $(this).val().toLowerCase();
        const suggestions = $('#product-suggestions');
        
        if (query.length < 2) {
            suggestions.empty();
            return;
        }
        
        const matches = products.filter(p => 
            p.name.toLowerCase().includes(query) || 
            p.sku.toLowerCase().includes(query)
        ).slice(0, 5);
        
        suggestions.empty();
        matches.forEach(product => {
            const item = $(`
                <a href="#" class="list-group-item list-group-item-action product-suggestion" 
                   data-product-id="${product.id}">
                    <div class="d-flex justify-content-between">
                        <div>
                            <strong>${product.name}</strong><br>
                            <small class="text-muted">${product.sku}</small>
                        </div>
                        <div class="text-end">
                            <div>$${product.selling_price}</div>
                            <small class="text-muted">${product.stock_quantity} in stock</small>
                        </div>
                    </div>
                </a>
            `);
            suggestions.append(item);
        });
    });
    
    // Add product from suggestions
    $(document).on('click', '.product-suggestion', function(e) {
        e.preventDefault();
        const productId = $(this).data('product-id');
        
        // Find empty item or add new one
        let emptyItem = $('.invoice-item').filter(function() {
            return $(this).find('.product-select').val() === '';
        }).first();
        
        if (emptyItem.length === 0) {
            $('#add-item').click();
            emptyItem = $('.invoice-item').last();
        }
        
        emptyItem.find('.product-select').val(productId).trigger('change');
        emptyItem.find('.quantity-input').val(1).trigger('input');
        
        $('#product-search').val('');
        $('#product-suggestions').empty();
    });
    
    // Initial calculation
    calculateTotals();
});
</script>
{% endblock %}
