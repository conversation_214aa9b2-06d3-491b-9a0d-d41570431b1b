from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, logout
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import CreateView, UpdateView, ListView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse
from django.core.paginator import Paginator
from .models import User
from .forms import CustomUserCreationForm, LoginForm, UserProfileForm


def is_admin(user):
    """Check if user is admin"""
    return user.is_authenticated and user.role == User.UserRole.ADMIN


class AdminRequiredMixin(UserPassesTestMixin):
    """Mixin to require admin role"""
    def test_func(self):
        return is_admin(self.request.user)


def login_view(request):
    """Custom login view"""
    if request.user.is_authenticated:
        return redirect('dashboard:home')

    if request.method == 'POST':
        form = LoginForm(request.POST)
        if form.is_valid():
            user = form.get_user()
            login(request, user)

            # Set session timeout based on remember_me
            if not form.cleaned_data.get('remember_me'):
                request.session.set_expiry(0)  # Session expires when browser closes

            messages.success(request, _('Welcome back, {}!').format(user.full_name or user.username))

            # Redirect to next page or dashboard
            next_page = request.GET.get('next', 'dashboard:home')
            return redirect(next_page)
    else:
        form = LoginForm()

    return render(request, 'accounts/login.html', {'form': form})


@login_required
def logout_view(request):
    """Logout view"""
    logout(request)
    messages.info(request, _('You have been logged out successfully.'))
    return redirect('accounts:login')


@login_required
def profile_view(request):
    """User profile view"""
    if request.method == 'POST':
        form = UserProfileForm(request.POST, request.FILES, instance=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, _('Profile updated successfully.'))
            return redirect('accounts:profile')
    else:
        form = UserProfileForm(instance=request.user)

    return render(request, 'accounts/profile.html', {'form': form})


class UserCreateView(AdminRequiredMixin, CreateView):
    """Create new user view (Admin only)"""
    model = User
    form_class = CustomUserCreationForm
    template_name = 'accounts/user_create.html'
    success_url = reverse_lazy('accounts:user_list')

    def form_valid(self, form):
        messages.success(self.request, _('User created successfully.'))
        return super().form_valid(form)


class UserListView(AdminRequiredMixin, ListView):
    """List all users view (Admin only)"""
    model = User
    template_name = 'accounts/user_list.html'
    context_object_name = 'users'
    paginate_by = 20

    def get_queryset(self):
        queryset = User.objects.all().order_by('-created_at')
        search = self.request.GET.get('search')
        role = self.request.GET.get('role')

        if search:
            queryset = queryset.filter(
                username__icontains=search
            ) | queryset.filter(
                first_name__icontains=search
            ) | queryset.filter(
                last_name__icontains=search
            ) | queryset.filter(
                email__icontains=search
            )

        if role:
            queryset = queryset.filter(role=role)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['roles'] = User.UserRole.choices
        context['search'] = self.request.GET.get('search', '')
        context['selected_role'] = self.request.GET.get('role', '')
        return context


class UserUpdateView(AdminRequiredMixin, UpdateView):
    """Update user view (Admin only)"""
    model = User
    fields = ['username', 'email', 'first_name', 'last_name', 'phone', 'role', 'is_active']
    template_name = 'accounts/user_update.html'
    success_url = reverse_lazy('accounts:user_list')

    def form_valid(self, form):
        messages.success(self.request, _('User updated successfully.'))
        return super().form_valid(form)


@login_required
@user_passes_test(is_admin)
def user_toggle_status(request, user_id):
    """Toggle user active status (Admin only)"""
    if request.method == 'POST':
        user = get_object_or_404(User, id=user_id)
        user.is_active = not user.is_active
        user.save()

        status = _('activated') if user.is_active else _('deactivated')
        messages.success(request, _('User {} has been {}.').format(user.username, status))

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'is_active': user.is_active,
                'message': str(messages.get_messages(request)[-1])
            })

    return redirect('accounts:user_list')


@login_required
def change_password_view(request):
    """Change password view"""
    from django.contrib.auth.forms import PasswordChangeForm

    if request.method == 'POST':
        form = PasswordChangeForm(request.user, request.POST)
        if form.is_valid():
            user = form.save()
            from django.contrib.auth import update_session_auth_hash
            update_session_auth_hash(request, user)  # Keep user logged in
            messages.success(request, _('Password changed successfully.'))
            return redirect('accounts:profile')
    else:
        form = PasswordChangeForm(request.user)

    return render(request, 'accounts/change_password.html', {'form': form})
