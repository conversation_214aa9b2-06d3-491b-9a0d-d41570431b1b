{% extends 'base.html' %}
{% load i18n crispy_forms_tags %}

{% block title %}{% trans "Products" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{% trans "Product Management" %}</h1>
            <p class="text-muted">{% trans "Manage your inventory and product catalog" %}</p>
        </div>
        <div class="btn-group" role="group">
            <a href="{% url 'products:product_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>{% trans "Add Product" %}
            </a>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-cog me-2"></i>{% trans "More" %}
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{% url 'products:category_list' %}">
                        <i class="fas fa-tags me-2"></i>{% trans "Categories" %}
                    </a></li>
                    <li><a class="dropdown-item" href="{% url 'products:supplier_list' %}">
                        <i class="fas fa-truck me-2"></i>{% trans "Suppliers" %}
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="{% url 'products:bulk_import' %}">
                        <i class="fas fa-upload me-2"></i>{% trans "Import Products" %}
                    </a></li>
                    <li><a class="dropdown-item" href="{% url 'products:export_products' %}">
                        <i class="fas fa-download me-2"></i>{% trans "Export Products" %}
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    {{ form.search.label_tag }}
                    {{ form.search }}
                </div>
                <div class="col-md-2">
                    {{ form.category.label_tag }}
                    {{ form.category }}
                </div>
                <div class="col-md-2">
                    {{ form.stock_status.label_tag }}
                    {{ form.stock_status }}
                </div>
                <div class="col-md-2">
                    {{ form.is_active.label_tag }}
                    {{ form.is_active }}
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search me-1"></i>{% trans "Filter" %}
                    </button>
                    <a href="{% url 'products:product_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Products Table -->
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-box me-2"></i>{% trans "Products" %}
                {% if products %}
                    <span class="badge bg-primary ms-2">{{ page_obj.paginator.count }}</span>
                {% endif %}
            </h6>
        </div>
        <div class="card-body p-0">
            {% if products %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>{% trans "Product" %}</th>
                                <th>{% trans "SKU" %}</th>
                                <th>{% trans "Category" %}</th>
                                <th>{% trans "Price" %}</th>
                                <th>{% trans "Stock" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if product.product_image %}
                                                <img src="{{ product.product_image.url }}" alt="{{ product.name }}"
                                                     class="rounded me-3" width="50" height="50" style="object-fit: cover;">
                                            {% else %}
                                                <div class="bg-light rounded d-flex align-items-center justify-content-center me-3"
                                                     style="width: 50px; height: 50px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                            {% endif %}
                                            <div>
                                                <div class="fw-bold">{{ product.name }}</div>
                                                {% if product.description %}
                                                    <small class="text-muted">{{ product.description|truncatechars:50 }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <code>{{ product.sku }}</code>
                                    </td>
                                    <td>
                                        {% if product.category %}
                                            <span class="badge bg-info">{{ product.category.name }}</span>
                                        {% else %}
                                            <span class="text-muted">{% trans "No Category" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div>${{ product.selling_price }}</div>
                                        <small class="text-muted">{% trans "Cost" %}: ${{ product.cost_price }}</small>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="me-2">{{ product.stock_quantity }}</span>
                                            {% if product.is_low_stock %}
                                                <span class="badge bg-warning">{% trans "Low" %}</span>
                                            {% elif product.stock_quantity == 0 %}
                                                <span class="badge bg-danger">{% trans "Out" %}</span>
                                            {% else %}
                                                <span class="badge bg-success">{% trans "OK" %}</span>
                                            {% endif %}
                                        </div>
                                        <small class="text-muted">{% trans "Min" %}: {{ product.min_stock_level }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if product.is_active %}success{% else %}danger{% endif %}">
                                            {% if product.is_active %}{% trans "Active" %}{% else %}{% trans "Inactive" %}{% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{% url 'products:product_detail' product.pk %}"
                                               class="btn btn-outline-info" title="{% trans 'View Details' %}">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'products:product_update' product.pk %}"
                                               class="btn btn-outline-primary" title="{% trans 'Edit Product' %}">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'products:product_delete' product.pk %}"
                                               class="btn btn-outline-danger delete-confirm"
                                               data-item-name="{{ product.name }}"
                                               title="{% trans 'Delete Product' %}">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                    <div class="card-footer">
                        <nav aria-label="{% trans 'Products pagination' %}">
                            <ul class="pagination justify-content-center mb-0">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                            {% trans "First" %}
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                            {% trans "Previous" %}
                                        </a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">
                                        {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                            {% trans "Next" %}
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                            {% trans "Last" %}
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-box text-muted" style="font-size: 3rem;"></i>
                    <h5 class="mt-3 text-muted">{% trans "No products found" %}</h5>
                    <p class="text-muted">{% trans "Start by adding your first product to the inventory." %}</p>
                    <a href="{% url 'products:product_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "Add First Product" %}
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
