from django.urls import path
from . import views

app_name = 'customers'

urlpatterns = [
    # Customer URLs
    path('', views.customer_list, name='customer_list'),
    path('create/', views.CustomerCreateView.as_view(), name='customer_create'),
    path('<int:pk>/', views.customer_detail, name='customer_detail'),
    path('<int:pk>/edit/', views.CustomerUpdateView.as_view(), name='customer_update'),
    path('<int:pk>/delete/', views.CustomerDeleteView.as_view(), name='customer_delete'),
    path('<int:pk>/history/', views.customer_purchase_history, name='customer_purchase_history'),

    # Export URLs
    path('export/', views.export_customers, name='export_customers'),

    # Outstanding balances
    path('outstanding-balances/', views.customers_with_outstanding_balance, name='outstanding_balances'),
]
