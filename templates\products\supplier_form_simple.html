{% extends 'base.html' %}
{% load i18n %}
{% load arabic_trans %}

{% block title %}
    {% if object %}
        {% trans "Edit Supplier" %} - {{ object.name }}
    {% else %}
        {% trans "Add New Supplier" %}
    {% endif %}
    - {{ block.super }}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                {% if object %}
                    {% trans "Edit Supplier" %}
                {% else %}
                    {% trans "Add New Supplier" %}
                {% endif %}
            </h1>
            <p class="text-muted">
                {% if object %}
                    {% trans "Update supplier information" %}
                {% else %}
                    {% trans "Add a new supplier to your system" %}
                {% endif %}
            </p>
        </div>
        <div>
            <a href="{% url 'products:supplier_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Suppliers" %}
            </a>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-8">
            <form method="post">
                {% csrf_token %}
                
                <!-- Basic Information -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>{% trans "Basic Information" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        {% trans "Supplier Name" %} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{% trans "Contact Person" %}</label>
                                    {{ form.contact_person }}
                                    {% if form.contact_person.errors %}
                                        <div class="text-danger small">{{ form.contact_person.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        {% trans "Phone" %} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.phone }}
                                    {% if form.phone.errors %}
                                        <div class="text-danger small">{{ form.phone.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{% trans "Email" %}</label>
                                    {{ form.email }}
                                    {% if form.email.errors %}
                                        <div class="text-danger small">{{ form.email.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{% trans "Website" %}</label>
                                    {{ form.website }}
                                    {% if form.website.errors %}
                                        <div class="text-danger small">{{ form.website.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{% trans "Company Registration" %}</label>
                                    {{ form.company_registration }}
                                    {% if form.company_registration.errors %}
                                        <div class="text-danger small">{{ form.company_registration.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Address Information -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-map-marker-alt me-2"></i>{% trans "Address Information" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">{% trans "Address" %}</label>
                            {{ form.address }}
                            {% if form.address.errors %}
                                <div class="text-danger small">{{ form.address.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{% trans "City" %}</label>
                                    {{ form.city }}
                                    {% if form.city.errors %}
                                        <div class="text-danger small">{{ form.city.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        {% trans "Country" %} <span class="text-danger">*</span>
                                    </label>
                                    {{ form.country }}
                                    {% if form.country.errors %}
                                        <div class="text-danger small">{{ form.country.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Additional Information -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-sticky-note me-2"></i>{% trans "Additional Information" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{% trans "Tax ID" %}</label>
                                    {{ form.tax_id }}
                                    {% if form.tax_id.errors %}
                                        <div class="text-danger small">{{ form.tax_id.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        {{ form.is_active }}
                                        <label class="form-check-label">{% trans "Active" %}</label>
                                    </div>
                                    {% if form.is_active.errors %}
                                        <div class="text-danger small">{{ form.is_active.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">{% trans "Notes" %}</label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Form Actions -->
                <div class="card mt-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                {% if object %}
                                    <a href="{% url 'products:supplier_delete' object.pk %}" 
                                       class="btn btn-outline-danger"
                                       onclick="return confirm('{% trans 'Are you sure you want to delete this supplier?' %}')">
                                        <i class="fas fa-trash me-2"></i>{% trans "Delete Supplier" %}
                                    </a>
                                {% endif %}
                            </div>
                            <div>
                                <a href="{% url 'products:supplier_list' %}" class="btn btn-outline-secondary me-2">
                                    {% trans "Cancel" %}
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    {% if object %}
                                        {% trans "Update Supplier" %}
                                    {% else %}
                                        {% trans "Create Supplier" %}
                                    {% endif %}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Help Card -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>{% trans "Help" %}
                    </h6>
                </div>
                <div class="card-body">
                    <h6>{% trans "Required Fields" %}</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success me-1"></i> {% trans "Supplier Name" %}</li>
                        <li><i class="fas fa-check text-success me-1"></i> {% trans "Phone" %}</li>
                        <li><i class="fas fa-check text-success me-1"></i> {% trans "Country" %}</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
