{% extends 'base.html' %}
{% load i18n %}
{% load arabic_trans %}

{% block title %}{% trans "Customers" %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{% trans "Customer Management" %}</h1>
            <p class="text-muted">{% trans "Manage your customer relationships and data" %}</p>
        </div>
        <div class="btn-group" role="group">
            <a href="{% url 'customers:customer_create' %}" class="btn btn-primary">
                <i class="fas fa-user-plus me-2"></i>{% trans "Add Customer" %}
            </a>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-cog me-2"></i>{% trans "More" %}
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{% url 'customers:export_customers' %}">
                        <i class="fas fa-download me-2"></i>{% trans "Export Customers" %}
                    </a></li>
                    {% if user.role == 'admin' or user.role == 'accountant' %}
                    <li><a class="dropdown-item" href="{% url 'customers:outstanding_balances' %}">
                        <i class="fas fa-exclamation-triangle me-2"></i>{% trans "Outstanding Balances" %}
                    </a></li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    {{ form.search.label_tag }}
                    {{ form.search }}
                </div>
                <div class="col-md-2">
                    {{ form.customer_type.label_tag }}
                    {{ form.customer_type }}
                </div>
                <div class="col-md-2">
                    {{ form.is_active.label_tag }}
                    {{ form.is_active }}
                </div>
                <div class="col-md-2">
                    {{ form.has_outstanding_balance.label_tag }}
                    {{ form.has_outstanding_balance }}
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search me-1"></i>{% trans "Filter" %}
                    </button>
                    <a href="{% url 'customers:customer_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Customers Table -->
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-users me-2"></i>{% trans "Customers" %}
                {% if customers %}
                    <span class="badge bg-primary ms-2">{{ page_obj.paginator.count }}</span>
                {% endif %}
            </h6>
        </div>
        <div class="card-body p-0">
            {% if customers %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>{% trans "Customer" %}</th>
                                <th>{% trans "Type" %}</th>
                                <th>{% trans "Contact" %}</th>
                                <th>{% trans "Credit Info" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in customers %}
                                <tr>
                                    <td>
                                        <div>
                                            <div class="fw-bold">{{ customer.name }}</div>
                                            {% if customer.company_name %}
                                                <small class="text-muted">{{ customer.company_name }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if customer.customer_type == 'business' %}info{% else %}secondary{% endif %}">
                                            {{ customer.get_customer_type_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div>{{ customer.phone }}</div>
                                        {% if customer.email %}
                                            <small class="text-muted">{{ customer.email }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div>{% trans "Limit" %}: ${{ customer.credit_limit }}</div>
                                        <small class="text-muted">{% trans "Terms" %}: {{ customer.payment_terms }} {% trans "days" %}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if customer.is_active %}success{% else %}danger{% endif %}">
                                            {% if customer.is_active %}{% trans "Active" %}{% else %}{% trans "Inactive" %}{% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{% url 'customers:customer_detail' customer.pk %}"
                                               class="btn btn-outline-info" title="{% trans 'View Details' %}">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'customers:customer_update' customer.pk %}"
                                               class="btn btn-outline-primary" title="{% trans 'Edit Customer' %}">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'customers:customer_delete' customer.pk %}"
                                               class="btn btn-outline-danger delete-confirm"
                                               data-item-name="{{ customer.name }}"
                                               title="{% trans 'Delete Customer' %}">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                    <div class="card-footer">
                        <nav aria-label="{% trans 'Customers pagination' %}">
                            <ul class="pagination justify-content-center mb-0">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                            {% trans "First" %}
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                            {% trans "Previous" %}
                                        </a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">
                                        {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                            {% trans "Next" %}
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                            {% trans "Last" %}
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users text-muted" style="font-size: 3rem;"></i>
                    <h5 class="mt-3 text-muted">{% trans "No customers found" %}</h5>
                    <p class="text-muted">{% trans "Start by adding your first customer to the system." %}</p>
                    <a href="{% url 'customers:customer_create' %}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>{% trans "Add First Customer" %}
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
